from __future__ import annotations
import os, json
from typing import Any
from .base import LLM

class OllamaProvider(LLM):
    """
    Minimal, robust Ollama provider.
    - No fragile preflight on client methods that vary by version.
    - Optional HTTP health check to /api/version (works across versions).
    """
    def __init__(self, model_env: str = "OLLAMA_MODEL", host_env: str = "OLLAMA_HOST"):
        try:
            from ollama import Client  # pip install ollama>=0.3.0
        except Exception as e:
            raise RuntimeError("Install ollama client: pip install 'ollama>=0.3.0'") from e

        self.host = os.getenv(host_env, "http://127.0.0.1:11434")
        self.model = os.getenv(model_env, "gemma3:270m")
        self._client = Client(host=self.host)

    def _health_check(self) -> None:
        """Portable, tolerant check; uses raw HTTP not client internals."""
        try:
            import requests  # add to deps if missing
            r = requests.get(f"{self.host}/api/version", timeout=3)
            r.raise_for_status()
        except Exception as e:
            raise RuntimeError(
                f"Ollama HTTP API not reachable at {self.host}. "
                f"Start it with 'ollama serve' and ensure the port is open."
            ) from e

    def generate(self, prompt: str) -> str:
        # Optional: uncomment if you want a pre-chat ping
        # self._health_check()

        sys = "You are a planner that returns STRICT JSON only. No markdown, no prose."
        try:
            resp = self._client.chat(
                model=self.model,
                messages=[
                    {"role": "system", "content": sys},
                    {"role": "user", "content": prompt},
                ],
                options={"temperature": 0.2},
            )
        except Exception as e:
            raise RuntimeError(
                f"Ollama chat failed. Host={self.host} Model={self.model}. "
                f"Ensure the model is pulled: `ollama pull {self.model}` and the daemon is running."
            ) from e

        return (resp.get("message", {}) or {}).get("content", "") or ""

    def chat(self, messages: list, system_prompt: str = None, temperature: float = 0.3) -> dict:
        """
        Chat with the Ollama model using conversation history.

        Args:
            messages: List of message dictionaries with 'role' and 'content'
            system_prompt: Optional system prompt to set context
            temperature: Sampling temperature (0.0 to 1.0)

        Returns:
            Dictionary with response content and token usage information
        """
        try:
            # Prepare messages with system prompt if provided
            chat_messages = []
            if system_prompt:
                chat_messages.append({"role": "system", "content": system_prompt})

            # Add conversation messages
            chat_messages.extend(messages)

            # Calculate approximate input tokens (rough estimation)
            input_text = ""
            for msg in chat_messages:
                input_text += msg.get("content", "")

            # Rough token estimation (1 token ≈ 4 characters for English)
            estimated_input_tokens = len(input_text) // 4

            # Debug logging for troubleshooting
            from loguru import logger
            logger.debug(f"Ollama chat - Total messages: {len(chat_messages)}")
            logger.debug(f"Ollama chat - System prompt length: {len(system_prompt) if system_prompt else 0}")
            logger.debug(f"Ollama chat - User message length: {len(messages[0].get('content', '')) if messages else 0}")
            logger.debug(f"Ollama chat - Estimated input tokens: {estimated_input_tokens}")

            logger.debug(f"chat_messages: {chat_messages}")

            resp = self._client.chat(
                model=self.model,
                messages=chat_messages,
                options={"temperature": temperature},
            )

            response_content = (resp.get("message", {}) or {}).get("content", "") or ""
            estimated_output_tokens = len(response_content) // 4

            # Return enhanced response with token information
            return {
                "content": response_content,
                "usage": {
                    "prompt_tokens": estimated_input_tokens,
                    "completion_tokens": estimated_output_tokens,
                    "total_tokens": estimated_input_tokens + estimated_output_tokens,
                    "model": self.model
                },
                "model": self.model,
                "raw_response": resp
            }

        except Exception as e:
            raise RuntimeError(
                f"Ollama chat failed. Host={self.host} Model={self.model}. "
                f"Error: {str(e)}"
            ) from e

    def parse_json(self, text: str) -> Any:
        try:
            return json.loads(text)
        except Exception:
            s, e = text.find("{"), text.rfind("}")
            if s != -1 and e != -1 and e > s:
                return json.loads(text[s:e+1])
            raise

from __future__ import annotations
from typing import Any, Dict, List
from loguru import logger
from .types import Planner, Message, Plan, Observation
from ..tools.registry import ToolRegistry
"""
Memory is now handled via agent-linked memory classes and adapters, initialized by AgentFactory and BaseAgent.
"""
from ..validation.base import Validator

class AgentRunner:
    def __init__(
        self,
        planner: Planner,
        tools: ToolRegistry,
        memory: Dict[str, Any],  # Dict of memory types/adapters
        validators: List[Validator],
        policy: Dict[str, Any],
    ):
        self.planner = planner
        self.tools = tools
        self.memory = memory  # Dict[str, Adapter]
        self.validators = validators
        self.policy = policy

    def run(self, run_id: str, goal: str, history: List[Message] | None = None, state: Dict[str, Any] | None = None) -> Dict[str, Any]:
        history = history or []
        state = state or {}
        max_steps: int = int(self.policy.get("max_steps", 8))
        allowed = set(self.policy.get("allowed_tools", []))

        for step in range(max_steps):
            # validators (budget/step/etc.) can write violations
            violations = []
            for v in self.validators:
                violations.extend(v.check({"step": step, "state": state, "history": history}))
            if violations:
                logger.warning(f"Violations: {violations}")
                return {"finish": True, "violations": violations, "state": state}

            plan: Plan = self.planner.plan(goal, history, state)
            logger.debug(f"Plan: {plan.model_dump()}")

            if not plan.actions:  # finish condition
                return {"finish": True, "result": state.get("answer"), "state": state}

            observations: List[Observation] = []
            for action in plan.actions:
                if action.tool not in allowed:
                    msg = f"Tool '{action.tool}' not allowed by policy"
                    logger.error(msg)
                    observations.append(Observation(tool=action.tool, output=None, ok=False, error=msg))
                    break

                tool = self.tools.get(action.tool)
                try:
                    out = tool(action.params)
                    obs = Observation(tool=action.tool, output=out, ok=True)
                except Exception as e:
                    obs = Observation(tool=action.tool, output=None, ok=False, error=str(e))
                observations.append(obs)
                # Save to appropriate memory type (short_term, episodic, etc.)
                if "short_term" in self.memory:
                    # Save tool observation to short term memory
                    msg = Message(role="tool", content=str(obs.model_dump()))
                    self.memory["short_term"].add_message(
                        run_id, "agent", "session", msg
                    )
                if "episodic" in self.memory:
                    self.memory["episodic"].log_event({
                        "run_id": run_id,
                        "action": action.model_dump(),
                        "observation": obs.model_dump()
                    })
                # Add to other memory types as needed

            # basic state stitching for next loop
            state.setdefault("observations", []).extend([o.model_dump() for o in observations])
            history.append(Message(role="tool", content="; ".join([str(o.output) for o in observations if o.ok])))

        return {"finish": False, "reason": "max_steps_reached", "state": state}

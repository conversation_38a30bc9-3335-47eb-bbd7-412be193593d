"""
Unit tests for Agent Factory.

This module contains comprehensive unit tests for the AgentFactory class,
testing agent creation, configuration handling, and error scenarios.
"""

import pytest
import tempfile
import json
import yaml
from pathlib import Path
from unittest.mock import patch, MagicMock

from app.factory.agent_factory import AgentFactory
from app.core.exceptions import FactoryError, ConfigurationError
from app.agents.base import BaseAgent
from app.agents.customer_support import CustomerSupportAgent
from app.agents.sales import SalesAgent
from app.agents.research import ResearchAgent


class TestAgentFactory:
    """Test cases for AgentFactory class."""
    
    def test_get_available_agent_types(self):
        """Test getting available agent types."""
        agent_types = AgentFactory.get_available_agent_types()
        
        assert isinstance(agent_types, list)
        assert "customer_support" in agent_types
        assert "sales" in agent_types
        assert "research" in agent_types
        assert len(agent_types) >= 3
    
    def test_get_default_config_valid_type(self):
        """Test getting default configuration for valid agent type."""
        config = AgentFactory.get_default_config("customer_support")
        
        assert isinstance(config, dict)
        assert "tools" in config
        assert "memory" in config
        assert "planner" in config
        assert "escalation_threshold" in config
    
    def test_get_default_config_invalid_type(self):
        """Test getting default configuration for invalid agent type."""
        with pytest.raises(FactoryError) as exc_info:
            AgentFactory.get_default_config("invalid_type")
        
        assert "Unknown agent type" in str(exc_info.value)
        assert "invalid_type" in str(exc_info.value)
    
    def test_create_agent_customer_support(self):
        """Test creating customer support agent."""
        agent = AgentFactory.create_agent("customer_support")
        
        assert isinstance(agent, CustomerSupportAgent)
        assert isinstance(agent, BaseAgent)
        assert agent.name == "customer_support"
        assert "ticket_management" in agent.capabilities
        assert "knowledge_base_search" in agent.capabilities
    
    def test_create_agent_sales(self):
        """Test creating sales agent."""
        agent = AgentFactory.create_agent("sales")
        
        assert isinstance(agent, SalesAgent)
        assert isinstance(agent, BaseAgent)
        assert agent.name == "sales"
        assert "lead_qualification" in agent.capabilities
        assert "product_recommendation" in agent.capabilities
    
    def test_create_agent_research(self):
        """Test creating research agent."""
        agent = AgentFactory.create_agent("research")
        
        assert isinstance(agent, ResearchAgent)
        assert isinstance(agent, BaseAgent)
        assert agent.name == "research"
        assert "information_gathering" in agent.capabilities
        assert "fact_verification" in agent.capabilities
    
    def test_create_agent_invalid_type(self):
        """Test creating agent with invalid type."""
        with pytest.raises(FactoryError) as exc_info:
            AgentFactory.create_agent("invalid_type")
        
        assert "Unknown agent type" in str(exc_info.value)
        assert "invalid_type" in str(exc_info.value)
    
    def test_create_agent_with_inline_config(self):
        """Test creating agent with inline configuration."""
        config = {
            "tools": ["custom_tool"],
            "memory": {"type": "vector", "size": 2000},
            "custom_param": "test_value"
        }
        
        agent = AgentFactory.create_agent("customer_support", config=config)
        
        assert isinstance(agent, CustomerSupportAgent)
        assert agent.config["custom_param"] == "test_value"
        assert agent.config["memory"]["type"] == "vector"
        assert agent.config["memory"]["size"] == 2000
    
    def test_create_agent_with_yaml_config_file(self):
        """Test creating agent with YAML configuration file."""
        config_data = {
            "tools": ["yaml_tool"],
            "memory": {"type": "persistent"},
            "escalation_threshold": 0.5
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.safe_dump(config_data, f)
            config_path = f.name
        
        try:
            agent = AgentFactory.create_agent("customer_support", config_path=config_path)
            
            assert isinstance(agent, CustomerSupportAgent)
            assert agent.config["escalation_threshold"] == 0.5
            assert agent.config["memory"]["type"] == "persistent"
        finally:
            Path(config_path).unlink()
    
    def test_create_agent_with_json_config_file(self):
        """Test creating agent with JSON configuration file."""
        config_data = {
            "tools": ["json_tool"],
            "memory": {"type": "redis"},
            "max_discount": 0.2
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            config_path = f.name
        
        try:
            agent = AgentFactory.create_agent("sales", config_path=config_path)
            
            assert isinstance(agent, SalesAgent)
            assert agent.config["max_discount"] == 0.2
            assert agent.config["memory"]["type"] == "redis"
        finally:
            Path(config_path).unlink()
    
    def test_create_agent_config_file_not_found(self):
        """Test creating agent with non-existent config file."""
        with pytest.raises(FactoryError) as exc_info:
            AgentFactory.create_agent("customer_support", config_path="/nonexistent/config.yaml")
        
        assert "Configuration file not found" in str(exc_info.value)
    
    def test_create_agent_invalid_config_format(self):
        """Test creating agent with invalid config file format."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("invalid config")
            config_path = f.name

        try:
            with pytest.raises(FactoryError) as exc_info:
                AgentFactory.create_agent("customer_support", config_path=config_path)
            
            assert "Unsupported configuration file format" in str(exc_info.value)
        finally:
            Path(config_path).unlink()
    
    def test_create_agent_invalid_yaml_content(self):
        """Test creating agent with invalid YAML content."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write("invalid: yaml: content: [")
            config_path = f.name

        try:
            with pytest.raises(FactoryError) as exc_info:
                AgentFactory.create_agent("customer_support", config_path=config_path)
            
            assert "Configuration parsing failed" in str(exc_info.value)
        finally:
            Path(config_path).unlink()
    
    def test_create_agent_invalid_json_content(self):
        """Test creating agent with invalid JSON content."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write('{"invalid": json content}')
            config_path = f.name

        try:
            with pytest.raises(FactoryError) as exc_info:
                AgentFactory.create_agent("customer_support", config_path=config_path)
            
            assert "Configuration parsing failed" in str(exc_info.value)
        finally:
            Path(config_path).unlink()
    
    def test_create_agent_config_merge_priority(self):
        """Test configuration merge priority (inline > file > default)."""
        # Create config file
        file_config = {
            "tools": ["file_tool"],
            "memory": {"type": "file_memory"},
            "escalation_threshold": 0.4
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.safe_dump(file_config, f)
            config_path = f.name
        
        # Inline config should override file config
        inline_config = {
            "escalation_threshold": 0.6,
            "custom_param": "inline_value"
        }
        
        try:
            agent = AgentFactory.create_agent(
                "customer_support",
                config_path=config_path,
                config=inline_config
            )
            
            # Inline config should take precedence
            assert agent.config["escalation_threshold"] == 0.6
            assert agent.config["custom_param"] == "inline_value"
            # File config should be preserved where not overridden
            assert agent.config["memory"]["type"] == "file_memory"
            # Default config should be preserved where not overridden
            assert "max_resolution_attempts" in agent.config
        finally:
            Path(config_path).unlink()
    
    def test_create_agent_with_custom_name(self):
        """Test creating agent with custom name."""
        custom_name = "my_custom_agent"
        agent = AgentFactory.create_agent("customer_support", name=custom_name)
        
        assert agent.config.get("custom_name") == custom_name
    
    def test_validate_config_invalid_tools_type(self):
        """Test configuration validation with invalid tools type."""
        config = {"tools": "invalid_tools_type"}  # Should be list
        
        with pytest.raises(ConfigurationError) as exc_info:
            AgentFactory.create_agent("customer_support", config=config)
        
        assert "'tools' must be a list" in str(exc_info.value)
    
    def test_validate_config_invalid_memory_type(self):
        """Test configuration validation with invalid memory type."""
        config = {"memory": "invalid_memory_type"}  # Should be dict
        
        with pytest.raises(ConfigurationError) as exc_info:
            AgentFactory.create_agent("customer_support", config=config)
        
        assert "'memory' must be a dictionary" in str(exc_info.value)
    
    def test_validate_config_invalid_escalation_threshold(self):
        """Test configuration validation with invalid escalation threshold."""
        config = {"escalation_threshold": 1.5}  # Should be between 0 and 1
        
        with pytest.raises(ConfigurationError) as exc_info:
            AgentFactory.create_agent("customer_support", config=config)
        
        assert "escalation_threshold must be a number between 0 and 1" in str(exc_info.value)
    
    def test_validate_config_invalid_max_discount(self):
        """Test configuration validation with invalid max discount."""
        config = {"max_discount": -0.1}  # Should be between 0 and 1
        
        with pytest.raises(ConfigurationError) as exc_info:
            AgentFactory.create_agent("sales", config=config)
        
        assert "max_discount must be a number between 0 and 1" in str(exc_info.value)
    
    def test_validate_config_invalid_max_sources(self):
        """Test configuration validation with invalid max sources."""
        config = {"max_sources": -5}  # Should be positive
        
        with pytest.raises(ConfigurationError) as exc_info:
            AgentFactory.create_agent("research", config=config)
        
        assert "max_sources must be a positive integer" in str(exc_info.value)
    
    def test_register_new_agent_type(self):
        """Test registering a new agent type."""
        class CustomAgent(BaseAgent):
            def __init__(self, config):
                super().__init__("custom", config)

            def process(self, query: str, context: dict) -> dict:
                return {"response": "custom response"}
        
        default_config = {"custom_param": "value"}
        
        AgentFactory.register_agent_type("custom", CustomAgent, default_config)
        
        # Test that new type is available
        assert "custom" in AgentFactory.get_available_agent_types()
        
        # Test creating agent of new type
        agent = AgentFactory.create_agent("custom")
        assert isinstance(agent, CustomAgent)
        assert agent.config["custom_param"] == "value"
    
    def test_register_invalid_agent_class(self):
        """Test registering invalid agent class."""
        class InvalidAgent:  # Doesn't extend BaseAgent
            pass

        # This should work at registration time, but fail when trying to create an agent
        default_config = {"test": "value"}
        AgentFactory.register_agent_type("invalid", InvalidAgent, default_config)

        # Now try to create an agent - this should fail
        with pytest.raises(FactoryError) as exc_info:
            AgentFactory.create_agent("invalid")
        
        assert "Agent creation failed" in str(exc_info.value)
    
    def test_save_config_template_yaml(self):
        """Test saving configuration template as YAML."""
        with tempfile.NamedTemporaryFile(suffix='.yaml', delete=False) as f:
            template_path = f.name
        
        try:
            AgentFactory.save_config_template("customer_support", template_path)
            
            # Verify file was created and contains expected content
            assert Path(template_path).exists()
            
            with open(template_path, 'r') as f:
                config = yaml.safe_load(f)
            
            assert isinstance(config, dict)
            assert "tools" in config
            assert "escalation_threshold" in config
        finally:
            Path(template_path).unlink()
    
    def test_save_config_template_json(self):
        """Test saving configuration template as JSON."""
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            template_path = f.name
        
        try:
            AgentFactory.save_config_template("sales", template_path)
            
            # Verify file was created and contains expected content
            assert Path(template_path).exists()
            
            with open(template_path, 'r') as f:
                config = json.load(f)
            
            assert isinstance(config, dict)
            assert "tools" in config
            assert "max_discount" in config
        finally:
            Path(template_path).unlink()
    
    def test_save_config_template_invalid_agent_type(self):
        """Test saving config template for invalid agent type."""
        with pytest.raises(FactoryError):
            AgentFactory.save_config_template("invalid_type", "/tmp/test.yaml")
    
    @patch('app.factory.agent_factory.logger')
    def test_logging_during_agent_creation(self, mock_logger):
        """Test that appropriate logging occurs during agent creation."""
        agent = AgentFactory.create_agent("customer_support")
        
        # Verify logging calls were made
        mock_logger.info.assert_called()
        mock_logger.debug.assert_called()
        
        # Check specific log messages
        log_calls = [call.args[0] for call in mock_logger.info.call_args_list]
        assert any("Creating agent of type: customer_support" in msg for msg in log_calls)
        assert any("Successfully created customer_support agent" in msg for msg in log_calls)


class TestAgentFactoryIntegration:
    """Integration tests for AgentFactory with real file system operations."""
    
    def test_end_to_end_agent_creation_with_file_config(self):
        """Test complete agent creation workflow with file configuration."""
        # Create a comprehensive config file
        config_data = {
            "tools": ["http_get", "knowledge_base"],
            "memory": {
                "type": "buffer",
                "max_size": 1500
            },
            "planner": {
                "type": "react",
                "max_steps": 8
            },
            "escalation_threshold": 0.25,
            "max_resolution_attempts": 5,
            "supported_languages": ["en", "es", "fr"],
            "custom_settings": {
                "priority_queue": True,
                "auto_escalate": False
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.safe_dump(config_data, f)
            config_path = f.name
        
        try:
            # Create agent with file config
            agent = AgentFactory.create_agent(
                "customer_support",
                config_path=config_path,
                name="integration_test_agent"
            )
            
            # Verify agent was created correctly
            assert isinstance(agent, CustomerSupportAgent)
            assert agent.name == "integration_test_agent"  # Should use custom name
            assert agent.config["custom_name"] == "integration_test_agent"
            assert agent.config["escalation_threshold"] == 0.25
            assert agent.config["max_resolution_attempts"] == 5
            assert agent.config["supported_languages"] == ["en", "es", "fr"]
            assert agent.config["custom_settings"]["priority_queue"] is True
            
            # Verify agent functionality
            agent_info = agent.get_info()
            assert agent_info["name"] == "integration_test_agent"  # Should use custom name
            assert agent_info["is_initialized"] is True
            assert len(agent_info["capabilities"]) > 0
            
            # Test health check
            health = agent.health_check()
            assert health["status"] == "healthy"
            
        finally:
            Path(config_path).unlink()
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from loguru import logger
from ..core.types import Message, MultiModalContent
from ..tools.registry import ToolRegistry
"""
Memory is now handled via agent-linked memory classes and adapters, initialized by AgentFactory and BaseAgent.
"""
from ..planners.base import Planner
from ..core.exceptions import AgentError, ConfigurationError
import traceback


class BaseAgent(ABC):
    """
    Abstract base class for all agents in the framework.
    
    This class provides the foundational structure and common functionality
    for all specialized agents. It handles initialization, capability management,
    and defines the contract that all agents must implement.
    
    Attributes:
        name (str): Unique identifier for the agent
        config (Dict[str, Any]): Configuration parameters for the agent
        tools (ToolRegistry): Registry of available tools
    memory (Optional[Dict[str, Any]]): Dict of memory adapters for conversation history
        planner (Optional[Planner]): Planning system for action generation
        capabilities (List[str]): List of agent capabilities
    """
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """
        Initialize the base agent with configuration.
        
        Args:
            name: Unique identifier for the agent
            config: Configuration dictionary containing agent parameters
            
        Raises:
            ConfigurationError: If required configuration is missing
        """
        try:
            self.name = self._validate_name(name)
            self.config = self._validate_config(config)
            self.tools = ToolRegistry()
            self.memory: Optional[Dict[str, Any]] = None
            self.planner: Optional[Planner] = None
            self.capabilities: List[str] = []
            self._is_initialized = False
            
            logger.info(f"Initializing agent: {self.name}")
            self._setup_agent()
            
        except Exception as e:
            logger.error(f"Failed to initialize agent {name}: {type(e).__name__}: {e}\n{traceback.format_exc()}")
            raise AgentError(f"Agent initialization failed: {type(e).__name__}: {e}") from e
    
    def _validate_name(self, name: str) -> str:
        """Validate agent name."""
        if not name or not isinstance(name, str):
            raise ConfigurationError("Agent name must be a non-empty string")
        return name.strip()
    
    def _validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate agent configuration."""
        if not isinstance(config, dict):
            raise ConfigurationError("Agent config must be a dictionary")
        return config
    
    def _setup_agent(self) -> None:
        """Setup agent-specific configurations."""
        try:
            # Load agent-specific tools
            if "tools" in self.config:
                self._load_tools(self.config["tools"])
            
            # Setup memory if configured
            if "memory" in self.config:
                self._setup_memory(self.config["memory"])
            
            # Setup planner if configured
            if "planner" in self.config:
                self._setup_planner(self.config["planner"])
                
            self._is_initialized = True
            logger.info(f"Agent {self.name} setup completed successfully")
            
        except Exception as e:
            logger.error(f"Agent setup failed for {self.name}: {type(e).__name__}: {e}\n{traceback.format_exc()}")
            raise
    
    def _load_tools(self, tool_config: List[str]) -> None:
        """Load specified tools for the agent."""
        for tool_name in tool_config:
            try:
                # Tool loading logic would go here
                logger.debug(f"Loading tool {tool_name} for agent {self.name}")
            except Exception as e:
                logger.warning(f"Failed to load tool {tool_name}: {str(e)}")
    
    def _setup_memory(self, memory_config: Dict[str, Any]) -> None:
        """
        Set up memory system with proper memory types and adapters.

        Args:
            memory_config: Memory configuration dictionary
        """
        logger.info(f"Setting up memory with config: {memory_config}")

        # Import adapters and memory manager
        from ..memory.base import MemoryManager
        from ..memory.adapters import InMemoryAdapter, JSONLAdapter
        from ..memory.adapters_sqlite import SQLiteAdapter
        from ..memory.adapters_redis import RedisAdapter
        from ..memory.adapters_vectordb import VectorDBAdapter

        # Create adapters based on configuration
        adapters = {}

        # Short Term Memory Adapter
        st_cfg = memory_config.get("short_term", {})
        st_adapter = st_cfg.get("adapter", "in_memory")
        if st_adapter == "in_memory":
            adapters["short_term"] = InMemoryAdapter()
        elif st_adapter == "redis":
            adapters["short_term"] = RedisAdapter(
                host=st_cfg.get("host", "localhost"),
                port=st_cfg.get("port", 6379),
                db=st_cfg.get("db", 0)
            )
        elif st_adapter == "sqlite":
            adapters["short_term"] = SQLiteAdapter(db_path=st_cfg.get("sqlite_path", "./short_term.sqlite"))

        # Episodic Memory Adapter
        ep_cfg = memory_config.get("episodic", {})
        ep_adapter = ep_cfg.get("adapter", "jsonl")
        if ep_adapter == "jsonl":
            adapters["episodic"] = JSONLAdapter(file_path=ep_cfg.get("jsonl_path", "./episodic.jsonl"))
        elif ep_adapter == "sqlite":
            adapters["episodic"] = SQLiteAdapter(db_path=ep_cfg.get("sqlite_path", "./episodic.sqlite"))
        elif ep_adapter == "redis":
            adapters["episodic"] = RedisAdapter(
                host=ep_cfg.get("host", "localhost"),
                port=ep_cfg.get("port", 6379),
                db=ep_cfg.get("db", 1)
            )

        # Procedural Memory Adapter
        pr_cfg = memory_config.get("procedural", {})
        pr_adapter = pr_cfg.get("adapter", "jsonl")
        if pr_adapter == "jsonl":
            adapters["procedural"] = JSONLAdapter(file_path=pr_cfg.get("jsonl_path", "./procedural.jsonl"))
        elif pr_adapter == "in_memory":
            adapters["procedural"] = InMemoryAdapter()
        elif pr_adapter == "sqlite":
            adapters["procedural"] = SQLiteAdapter(db_path=pr_cfg.get("sqlite_path", "./procedural.sqlite"))

        # Semantic Memory Adapter
        se_cfg = memory_config.get("semantic", {})
        se_adapter = se_cfg.get("adapter", "vectordb")
        if se_adapter == "vectordb":
            adapters["semantic"] = VectorDBAdapter(
                url=se_cfg.get("url", "http://localhost:6333"),
                api_key=se_cfg.get("qdrant_api_key", None),
                collection=se_cfg.get("collection", "default_collection")
            )

        # Create memory manager with adapters
        self.memory_manager = MemoryManager(adapters=adapters)

        # For backward compatibility, create a memory dict that routes to memory manager
        # This allows existing code to work while we transition to the new system
        self.memory = {
            "short_term": self.memory_manager,  # Routes will use memory_manager methods
            "episodic": self.memory_manager,
            "procedural": self.memory_manager,
            "semantic": self.memory_manager
        }

        logger.info(f"Memory setup complete with adapters: {list(adapters.keys())}")
        logger.info(f"Memory manager initialized with {len(adapters)} adapters")
    
    def _setup_planner(self, planner_config: Dict[str, Any]) -> None:
        """Setup planner for the agent."""
        logger.debug(f"Setting up planner for agent {self.name}")
        # Planner setup logic would go here
    
    @abstractmethod
    def process(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a user query and return a response.
        
        This is the main entry point for agent processing. Each specialized
        agent must implement this method with their specific logic.
        
        Args:
            query: User input query
            context: Additional context information
            
        Returns:
            Dict containing the agent's response and metadata
            
        Raises:
            AgentError: If processing fails
        """
        pass
    
    def add_capability(self, capability: str) -> None:
        """
        Add a capability to the agent.
        
        Args:
            capability: Name of the capability to add
        """
        if capability and capability not in self.capabilities:
            self.capabilities.append(capability)
            logger.debug(f"Added capability '{capability}' to agent {self.name}")
    
    def remove_capability(self, capability: str) -> None:
        """
        Remove a capability from the agent.
        
        Args:
            capability: Name of the capability to remove
        """
        if capability in self.capabilities:
            self.capabilities.remove(capability)
            logger.debug(f"Removed capability '{capability}' from agent {self.name}")
    
    def has_capability(self, capability: str) -> bool:
        """
        Check if agent has a specific capability.
        
        Args:
            capability: Name of the capability to check
            
        Returns:
            True if agent has the capability, False otherwise
        """
        return capability in self.capabilities
    
    def get_info(self) -> Dict[str, Any]:
        """
        Get agent information and status.
        
        Returns:
            Dictionary containing agent metadata
        """
        return {
            "name": self.name,
            "capabilities": self.capabilities,
            "is_initialized": self._is_initialized,
            "config": self.config,
            "tools_count": len(self.tools.list()) if self.tools else 0
        }
    
    def __str__(self) -> str:
        return f"Agent(name={self.name}, capabilities={len(self.capabilities)})"
    
    def __repr__(self) -> str:
        return f"Agent(name='{self.name}', capabilities={self.capabilities})"

    def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the agent.

        Returns:
            Dictionary containing health status information
        """
        try:
            import time

            # Calculate uptime (simplified - would be more complex in production)
            uptime = time.time() - getattr(self, '_start_time', time.time())

            health_status = {
                "status": "healthy" if self._is_initialized else "unhealthy",
                "name": self.name,
                "uptime": uptime,
                "capabilities_count": len(self.capabilities),
                "error": None
            }

            # Additional health checks
            if not self._is_initialized:
                health_status["error"] = "Agent not properly initialized"
                health_status["status"] = "unhealthy"
            elif not self.capabilities:
                health_status["error"] = "No capabilities available"
                health_status["status"] = "degraded"

            return health_status

        except Exception as e:
            return {
                "status": "unhealthy",
                "name": self.name,
                "uptime": 0.0,
                "capabilities_count": 0,
                "error": f"Health check failed: {str(e)}"
            }


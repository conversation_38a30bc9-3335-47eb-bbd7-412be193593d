from __future__ import annotations
from typing import Any, Dict, Callable
from pydantic import BaseModel

class ToolSpec(BaseModel):
    name: str
    description: str
    input_schema: Dict[str, Any]
    output_schema: Dict[str, Any]

class Tool:
    def __init__(self, spec: ToolSpec, fn: Callable[[Dict[str, Any]], Any]):
        self.spec, self.fn = spec, fn
    def __call__(self, params: Dict[str, Any]) -> Any:
        return self.fn(params)

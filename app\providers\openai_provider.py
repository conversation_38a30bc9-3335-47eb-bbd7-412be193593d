from __future__ import annotations
import os, json
from .base import LLM

class OpenAIProvider(LLM):
    def __init__(self, model_env: str = "LLM_MODEL"):
        try:
            from openai import OpenAI  # optional dep
        except Exception as e:
            raise RuntimeError("Install optional dependency: pip install '.[openai]'") from e
        self.model = os.getenv(model_env, "gpt-4o-mini")
        self.client = OpenAI()

    def generate(self, prompt: str) -> str:
        resp = self.client.chat.completions.create(
            model=self.model,
            messages=[{"role":"system","content":"Return only JSON."},
                      {"role":"user","content":prompt}],
            temperature=0.2,
        )
        return resp.choices[0].message.content

from __future__ import annotations
import yaml
import json
from pathlib import Path
from typing import Any, Dict, Union

from ..core.exceptions import PolicyLoadError


def load_policy(path: Union[str, Path]) -> Dict[str, Any]:
    """
    Load policy configuration from a file.

    Supports YAML and JSON policy files. The file format is
    determined by the file extension.

    Args:
        path: Path to the policy file

    Returns:
        Dictionary containing the loaded policy

    Raises:
        PolicyLoadError: If file cannot be loaded or parsed
    """
    try:
        policy_file = Path(path)

        if not policy_file.exists():
            raise PolicyLoadError(f"Policy file not found: {path}")

        with open(policy_file, 'r', encoding='utf-8') as f:
            if policy_file.suffix.lower() in ['.yaml', '.yml']:
                try:
                    policy = yaml.safe_load(f)
                except yaml.YAMLError as e:
                    raise PolicyLoadError(f"Failed to parse YAML policy file: {str(e)}") from e
            elif policy_file.suffix.lower() == '.json':
                try:
                    policy = json.load(f)
                except json.JSONDecodeError as e:
                    raise PolicyLoadError(f"Failed to parse JSON policy file: {str(e)}") from e
            else:
                raise PolicyLoadError(
                    f"Unsupported policy file format: {policy_file.suffix}. "
                    "Supported formats: .yaml, .yml, .json"
                )

        return policy or {}

    except PolicyLoadError:
        raise
    except PermissionError as e:
        raise PolicyLoadError(f"Permission denied reading policy file: {path}") from e
    except Exception as e:
        raise PolicyLoadError(f"Failed to read policy file: {str(e)}") from e

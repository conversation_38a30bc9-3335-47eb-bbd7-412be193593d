"""
Unit tests for VectorDB memory adapter.

This module contains comprehensive unit tests for VectorDBAdapter
including HTTP fallback functionality and error handling.
"""

import pytest
from unittest.mock import Mock, patch

from app.memory.adapters_vectordb import VectorDBAdapter


class TestVectorDBAdapter:
    """Test cases for VectorDBAdapter class."""

    def setup_method(self):
        """Set up test fixtures."""
        # Configuration parameters for VectorDBAdapter
        self.url = "http://localhost:6333"
        self.api_key = None
        self.collection = "test_collection"
        self.vector_size = 768
        self.distance = "Cosine"

    @patch('app.memory.adapters_vectordb.QdrantClient')
    @patch('app.memory.adapters_vectordb.requests')
    def test_initialization_success(self, mock_requests, mock_qdrant_client):
        """Test successful VectorDBAdapter initialization."""
        # Mock successful HTTP response for collection check
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"result": {"collections": []}}
        mock_requests.get.return_value = mock_response

        # Mock Qdrant client to raise NotImplementedError (forcing HTTP fallback)
        mock_client = Mock()
        mock_client.get_collections.side_effect = NotImplementedError()
        mock_qdrant_client.return_value = mock_client

        adapter = VectorDBAdapter(
            url=self.url,
            api_key=self.api_key,
            collection=self.collection,
            vector_size=self.vector_size,
            distance=self.distance
        )

        assert adapter.collection == "test_collection"
        assert adapter.api_key is None
        assert adapter.base_url == "http://localhost:6333"
        assert adapter.vector_size == 768
        assert adapter.distance == "Cosine"

    @patch('app.memory.adapters_vectordb.QdrantClient')
    @patch('app.memory.adapters_vectordb.requests')
    def test_convert_key_to_id(self, mock_requests, mock_qdrant_client):
        """Test key to ID conversion."""
        # Mock successful HTTP response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"result": {"collections": []}}
        mock_requests.get.return_value = mock_response

        adapter = VectorDBAdapter(
            url=self.url,
            api_key=self.api_key,
            collection=self.collection,
            vector_size=self.vector_size,
            distance=self.distance
        )

        # Test deterministic UUID generation
        key1 = "test_key_1"
        key2 = "test_key_2"

        id1 = adapter._convert_key_to_id(key1)
        id2 = adapter._convert_key_to_id(key2)

        # Should be valid UUID format
        assert len(id1) == 36  # UUID format: 8-4-4-4-12
        assert len(id2) == 36
        assert id1 != id2  # Different keys should produce different IDs

        # Should be deterministic
        assert adapter._convert_key_to_id(key1) == id1
        assert adapter._convert_key_to_id(key2) == id2

    @patch('app.memory.adapters_vectordb.QdrantClient')
    @patch('app.memory.adapters_vectordb.requests')
    def test_http_fallback_get_collections(self, mock_requests, mock_qdrant_client):
        """Test HTTP fallback for getting collections."""
        # Mock Qdrant client to raise NotImplementedError
        mock_client = Mock()
        mock_client.get_collections.side_effect = NotImplementedError()
        mock_qdrant_client.return_value = mock_client

        # Mock HTTP responses
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"result": {"collections": [{"name": "test_collection"}]}}
        mock_requests.get.return_value = mock_response

        adapter = VectorDBAdapter(
            url=self.url,
            api_key=self.api_key,
            collection=self.collection,
            vector_size=self.vector_size,
            distance=self.distance
        )
        collections = adapter._http_get_collections()

        assert collections == [{"name": "test_collection"}]
        mock_requests.get.assert_called()

    @patch('app.memory.adapters_vectordb.QdrantClient')
    @patch('app.memory.adapters_vectordb.requests')
    def test_http_fallback_create_collection(self, mock_requests, mock_qdrant_client):
        """Test HTTP fallback for creating collection."""
        # Mock Qdrant client to raise NotImplementedError
        mock_client = Mock()
        mock_client.get_collections.side_effect = NotImplementedError()
        mock_client.create_collection.side_effect = NotImplementedError()
        mock_qdrant_client.return_value = mock_client

        # Mock HTTP responses
        mock_get_response = Mock()
        mock_get_response.status_code = 200
        mock_get_response.json.return_value = {"result": {"collections": []}}

        mock_put_response = Mock()
        mock_put_response.status_code = 200
        mock_put_response.json.return_value = {"result": True}

        mock_requests.get.return_value = mock_get_response
        mock_requests.put.return_value = mock_put_response

        adapter = VectorDBAdapter(
            url=self.url,
            api_key=self.api_key,
            collection=self.collection,
            vector_size=self.vector_size,
            distance=self.distance
        )

        # Should not raise exception
        adapter._http_create_collection()
        mock_requests.put.assert_called()

    @patch('app.memory.adapters_vectordb.QdrantClient')
    @patch('app.memory.adapters_vectordb.requests')
    def test_http_fallback_add_vector(self, mock_requests, mock_qdrant_client):
        """Test HTTP fallback for adding vectors."""
        # Mock Qdrant client to raise NotImplementedError
        mock_client = Mock()
        mock_client.get_collections.side_effect = NotImplementedError()
        mock_client.upsert.side_effect = NotImplementedError()
        mock_qdrant_client.return_value = mock_client

        # Mock HTTP responses
        mock_get_response = Mock()
        mock_get_response.status_code = 200
        mock_get_response.json.return_value = {"result": {"collections": []}}

        mock_put_response = Mock()
        mock_put_response.status_code = 200
        mock_put_response.json.return_value = {"result": {"operation_id": 1}}

        mock_requests.get.return_value = mock_get_response
        mock_requests.put.return_value = mock_put_response

        adapter = VectorDBAdapter(
            url=self.url,
            api_key=self.api_key,
            collection=self.collection,
            vector_size=self.vector_size,
            distance=self.distance
        )

        # Test adding vector (use correct vector size)
        key = "test_key"
        embedding = [0.1] * 768  # Match the vector_size
        payload = {"text": "test document"}

        # Should not raise exception
        adapter.add(key, embedding, payload)

        # Verify HTTP request was made
        mock_requests.put.assert_called()

        # Check that the request included the converted ID and enhanced payload
        call_args = mock_requests.put.call_args
        json_data = call_args[1]['json']

        assert 'points' in json_data
        assert len(json_data['points']) == 1
        point = json_data['points'][0]

        assert 'id' in point
        assert 'vector' in point
        assert 'payload' in point
        assert point['vector'] == embedding
        assert point['payload']['_original_key'] == key
        assert point['payload']['text'] == "test document"

    @patch('app.memory.adapters_vectordb.QdrantClient')
    @patch('app.memory.adapters_vectordb.requests')
    def test_http_fallback_search_vectors(self, mock_requests, mock_qdrant_client):
        """Test HTTP fallback for searching vectors."""
        # Mock Qdrant client to raise NotImplementedError
        mock_client = Mock()
        mock_client.get_collections.side_effect = NotImplementedError()
        mock_client.search.side_effect = NotImplementedError()
        mock_qdrant_client.return_value = mock_client

        # Mock HTTP responses
        mock_get_response = Mock()
        mock_get_response.status_code = 200
        mock_get_response.json.return_value = {"result": {"collections": []}}

        mock_post_response = Mock()
        mock_post_response.status_code = 200
        mock_post_response.json.return_value = {
            "result": [
                {
                    "id": "test-uuid",
                    "score": 0.95,
                    "payload": {"text": "test document", "_original_key": "test_key"}
                }
            ]
        }

        mock_requests.get.return_value = mock_get_response
        mock_requests.post.return_value = mock_post_response

        adapter = VectorDBAdapter(
            url=self.url,
            api_key=self.api_key,
            collection=self.collection,
            vector_size=self.vector_size,
            distance=self.distance
        )

        # Test searching vectors (use correct vector size)
        query_embedding = [0.1] * 768  # Match the vector_size
        results = adapter.search(query_embedding, limit=5)

        assert len(results) == 1
        assert results[0]['text'] == "test document"
        assert results[0]['_original_key'] == "test_key"

        # Verify HTTP request was made
        mock_requests.post.assert_called()

    @patch('app.memory.adapters_vectordb.QdrantClient')
    @patch('app.memory.adapters_vectordb.requests')
    def test_http_fallback_clear_collection(self, mock_requests, mock_qdrant_client):
        """Test HTTP fallback for clearing collection."""
        # Mock Qdrant client to raise NotImplementedError
        mock_client = Mock()
        mock_client.get_collections.side_effect = NotImplementedError()
        mock_client.delete_collection.side_effect = NotImplementedError()
        mock_qdrant_client.return_value = mock_client

        # Mock HTTP responses
        mock_get_response = Mock()
        mock_get_response.status_code = 200
        mock_get_response.json.return_value = {"result": {"collections": []}}

        mock_delete_response = Mock()
        mock_delete_response.status_code = 200

        mock_put_response = Mock()
        mock_put_response.status_code = 200
        mock_put_response.json.return_value = {"result": True}

        mock_requests.get.return_value = mock_get_response
        mock_requests.delete.return_value = mock_delete_response
        mock_requests.put.return_value = mock_put_response

        adapter = VectorDBAdapter(
            url=self.url,
            api_key=self.api_key,
            collection=self.collection,
            vector_size=self.vector_size,
            distance=self.distance
        )

        # Test clearing collection
        adapter.clear()

        # Verify HTTP requests were made (delete and put for recreate)
        mock_requests.delete.assert_called()
        mock_requests.put.assert_called()

    @patch('app.memory.adapters_vectordb.QdrantClient')
    @patch('app.memory.adapters_vectordb.requests')
    def test_http_fallback_get_collection_info(self, mock_requests, mock_qdrant_client):
        """Test HTTP fallback for getting collection info."""
        # Mock Qdrant client to raise NotImplementedError
        mock_client = Mock()
        mock_client.get_collections.side_effect = NotImplementedError()
        mock_client.get_collection.side_effect = NotImplementedError()
        mock_qdrant_client.return_value = mock_client

        # Mock HTTP responses
        mock_get_response = Mock()
        mock_get_response.status_code = 200
        mock_get_response.json.return_value = {
            "result": {
                "collections": [],
                "status": "green",
                "vectors_count": 10,
                "indexed_vectors_count": 10,
                "points_count": 10
            }
        }

        mock_requests.get.return_value = mock_get_response

        adapter = VectorDBAdapter(
            url=self.url,
            api_key=self.api_key,
            collection=self.collection,
            vector_size=self.vector_size,
            distance=self.distance
        )

        # Test getting collection info
        info = adapter.get_collection_info()

        expected_info = {
            "name": "test_collection",
            "status": "green",
            "vectors_count": 10,
            "indexed_vectors_count": 10,
            "points_count": 10
        }

        assert info == expected_info

    @patch('app.memory.adapters_vectordb.QdrantClient')
    @patch('app.memory.adapters_vectordb.requests')
    def test_error_handling_http_requests(self, mock_requests, mock_qdrant_client):
        """Test error handling for HTTP requests."""
        # Mock Qdrant client to raise NotImplementedError
        mock_client = Mock()
        mock_client.get_collections.side_effect = NotImplementedError()
        mock_qdrant_client.return_value = mock_client

        # Mock HTTP error response
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.raise_for_status.side_effect = Exception("Server error")
        mock_requests.get.return_value = mock_response

        # Should raise exception during initialization due to HTTP error
        with pytest.raises(Exception):
            adapter = VectorDBAdapter(
                url=self.url,
                api_key=self.api_key,
                collection=self.collection,
                vector_size=self.vector_size,
                distance=self.distance
            )

    @patch('app.memory.adapters_vectordb.QdrantClient')
    @patch('app.memory.adapters_vectordb.requests')
    def test_initialization_with_api_key(self, mock_requests, mock_qdrant_client):
        """Test initialization with API key."""
        test_api_key = "test-api-key"

        # Mock successful HTTP response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"result": {"collections": []}}
        mock_requests.get.return_value = mock_response

        adapter = VectorDBAdapter(
            url=self.url,
            api_key=test_api_key,
            collection=self.collection,
            vector_size=self.vector_size,
            distance=self.distance
        )

        assert adapter.api_key == test_api_key

        # Verify API key is included in HTTP requests
        adapter._http_get_collections()

        call_args = mock_requests.get.call_args
        headers = call_args[1]['headers']
        assert headers.get('api-key') == test_api_key

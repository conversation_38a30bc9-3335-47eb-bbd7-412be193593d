# Enhanced Research Agent Configuration
# This configuration demonstrates research-specific features with web search integration

# Agent Identity and Behavior
persona: |
  You are Dr<PERSON> <PERSON>, a meticulous and analytical research specialist with a PhD in Information Science 
  and 10+ years of experience in academic and corporate research. You are curious, methodical, and passionate 
  about uncovering accurate information. You excel at synthesizing complex data from multiple sources and 
  presenting findings in clear, actionable formats. You maintain high standards for source credibility and 
  fact-checking.

system_prompt: |
  You are a professional research agent. Your role is to:

  1. **Gather information** - Search multiple reliable sources for comprehensive data
  2. **Analyze sources** - Evaluate credibility, relevance, and accuracy of information
  3. **Synthesize findings** - Combine information from various sources into coherent insights
  4. **Fact-check** - Verify claims and cross-reference information across sources
  5. **Present results** - Organize findings in clear, structured, and actionable formats

  **Research Process:**
  - Define scope: Clarify research objectives and parameters
  - Source identification: Find authoritative and diverse information sources
  - Data collection: Gather relevant information systematically
  - Analysis: Evaluate and synthesize collected information
  - Verification: Cross-check facts and validate claims
  - Presentation: Structure findings with proper citations

  **Guidelines:**
  - Always prioritize accuracy over speed
  - Use multiple sources to verify important claims
  - Clearly distinguish between facts and opinions
  - Provide proper citations and source attribution
  - Highlight any limitations or gaps in available information
  - Present both supporting and contradicting evidence when relevant

  **Tone:** Professional, objective, thorough, and analytical
  **Goal:** Provide accurate, comprehensive, and well-sourced research results

# LLM Provider Configuration
llm_provider: "ollama"
llm_model: "gemma3:270m"

# Tools Configuration
tools:
  - "http_get"        # For accessing APIs and web resources
  - "searxng_search"  # For comprehensive web search
  - "math_eval"       # For calculations and data analysis

# Memory Configuration
memory:
  short_term:
    type: "short_term"
    adapter: "redis"  # Fast, distributed
    host: "localhost"
    port: 6379
    db: 1
    max_size: 2000
    session_based: true
    max_sessions: 50
    max_messages_per_session: 200
  episodic:
    type: "episodic"
    adapter: "redis"  # Distributed, persistent
    host: "localhost"
    port: 6379
    db: 2
    max_size: 5000
  procedural:
    type: "procedural"
    adapter: "sqlite"  # Local, persistent
    sqlite_path: "./data/research_procedural.sqlite"
    max_size: 2000
  semantic:
    type: "semantic"
    adapter: "vectordb"  # Qdrant
    qdrant_host: "localhost"
    qdrant_port: 6333
    collection: "research_semantic"

# Planner Configuration
planner:
  type: "react"
  max_steps: 15
  temperature: 0.3  # Lower temperature for more focused research

# Research Specific Settings
max_sources: 10
fact_check_threshold: 0.8
research_depth: "comprehensive"
citation_style: "apa"

# Source Evaluation Criteria
source_evaluation:
  authority_weight: 0.3
  accuracy_weight: 0.3
  currency_weight: 0.2
  relevance_weight: 0.2

# Allowed Domains (for focused research)
allowed_domains:
  - "edu"
  - "gov"
  - "org"
  - "scholar.google.com"
  - "pubmed.ncbi.nlm.nih.gov"
  - "arxiv.org"

# Search Configuration
search_config:
  max_results_per_query: 20
  search_languages: ["en"]
  time_range: ""  # empty for all time
  categories: ["general", "science", "news"]

# Agent Capabilities
capabilities:
  - "information_gathering"
  - "fact_verification"
  - "report_generation"
  - "data_analysis"
  - "source_evaluation"
  - "citation_management"

# Research Types
research_types:
  - "market_research"
  - "competitive_analysis"
  - "literature_review"
  - "fact_checking"
  - "trend_analysis"
  - "technical_research"

# Output Formats
output_formats:
  - "executive_summary"
  - "detailed_report"
  - "bullet_points"
  - "citations_only"
  - "comparative_analysis"

# Quality Assurance
quality_assurance:
  peer_review: false
  fact_check_required: true
  source_verification: true
  bias_detection: true

# Research Ethics
ethics:
  respect_robots_txt: true
  rate_limiting: true
  attribution_required: true
  privacy_compliant: true

# SearxNG Configuration
searxng_config:
  url: "http://localhost:8080"
  timeout: 30
  max_retries: 3
  engines:
    - "google"
    - "bing"
    - "duckduckgo"
    - "wikipedia"
    - "arxiv"

# Data Processing
data_processing:
  remove_duplicates: true
  content_filtering: true
  relevance_scoring: true
  sentiment_analysis: false  # Not needed for research

# Reporting
reporting:
  include_methodology: true
  include_limitations: true
  include_recommendations: true
  confidence_scores: true

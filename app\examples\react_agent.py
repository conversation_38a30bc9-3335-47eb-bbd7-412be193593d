# examples/react_agent.py

from app.core.runner import Agent<PERSON>un<PERSON>
from app.tools.registry import ToolRegistry
from app.memory.buffer_memory import BufferMemory
from app.providers.openai_provider import OpenAIProvider
from app.planners.react_planner import ReactPlanner

def main():
    # Initialize components
    memory = BufferMemory()
    tools = ToolRegistry()
    tools.register_builtin_tools()  # Assumes http.get and math.eval are registered here
    llm = OpenAIProvider()
    planner = ReactPlanner(llm=llm, tools=tools)
    agent = AgentRunner(planner=planner, memory=memory)

    # User question
    question = "What is the latest news about AI in 2025? Please provide sources."

    # Run agent loop
    result = agent.run(question)
    print("Agent Answer:\n", result['answer'])
    print("\nCitations:")
    for citation in result.get('citations', []):
        print("-", citation)

if __name__ == "__main__":
    main()
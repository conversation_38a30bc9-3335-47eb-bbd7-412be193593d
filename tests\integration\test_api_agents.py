"""
Integration tests for Agent API endpoints.

This module contains comprehensive integration tests for all agent-related
API endpoints, testing the complete request-response cycle.
"""

import pytest
import json
import tempfile
import yaml
from pathlib import Path
from fastapi.testclient import TestClient

from app.api.main import app
from app.factory.agent_factory import AgentFactory


@pytest.fixture
def client():
    """Create test client for API testing."""
    return TestClient(app)


@pytest.fixture
def sample_config_file():
    """Create a sample configuration file for testing."""
    config_data = {
        "tools": ["test_tool"],
        "memory": {"type": "test_memory"},
        "escalation_threshold": 0.4,
        "max_resolution_attempts": 4
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yaml.safe_dump(config_data, f)
        yield f.name
    
    # Cleanup
    Path(f.name).unlink()


class TestAgentCreationAPI:
    """Test agent creation API endpoints."""
    
    def test_create_customer_support_agent_default_config(self, client):
        """Test creating customer support agent with default configuration."""
        request_data = {
            "agent_type": "customer_support"
        }
        
        response = client.post("/agent-factory/agents/create", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "agent_id" in data
        assert data["agent_type"] == "customer_support"
        assert data["status"] == "active"
        assert isinstance(data["capabilities"], list)
        assert len(data["capabilities"]) > 0
        assert "ticket_management" in data["capabilities"]
        assert "created_at" in data
    
    def test_create_sales_agent_default_config(self, client):
        """Test creating sales agent with default configuration."""
        request_data = {
            "agent_type": "sales"
        }
        
        response = client.post("/agents/create", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["agent_type"] == "sales"
        assert "lead_qualification" in data["capabilities"]
        assert "product_recommendation" in data["capabilities"]
    
    def test_create_research_agent_default_config(self, client):
        """Test creating research agent with default configuration."""
        request_data = {
            "agent_type": "research"
        }
        
        response = client.post("/agents/create", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["agent_type"] == "research"
        assert "information_gathering" in data["capabilities"]
        assert "fact_verification" in data["capabilities"]
    
    def test_create_agent_with_inline_config(self, client):
        """Test creating agent with inline configuration."""
        request_data = {
            "agent_type": "customer_support",
            "config": {
                "escalation_threshold": 0.6,
                "max_resolution_attempts": 6,
                "custom_param": "test_value"
            },
            "name": "custom_support_agent"
        }
        
        response = client.post("/agents/create", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["agent_type"] == "customer_support"
        assert "agent_id" in data
    
    def test_create_agent_with_config_file(self, client, sample_config_file):
        """Test creating agent with configuration file."""
        request_data = {
            "agent_type": "customer_support",
            "config_path": sample_config_file
        }
        
        response = client.post("/agents/create", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["agent_type"] == "customer_support"
        assert "agent_id" in data
    
    def test_create_agent_invalid_type(self, client):
        """Test creating agent with invalid type."""
        request_data = {
            "agent_type": "invalid_agent_type"
        }
        
        response = client.post("/agents/create", json=request_data)
        
        assert response.status_code == 400
        data = response.json()
        
        assert "detail" in data
        assert "Unknown agent type" in data["detail"]
        assert "invalid_agent_type" in data["detail"]
    
    def test_create_agent_invalid_config_file(self, client):
        """Test creating agent with non-existent config file."""
        request_data = {
            "agent_type": "customer_support",
            "config_path": "/nonexistent/config.yaml"
        }
        
        response = client.post("/agents/create", json=request_data)
        
        assert response.status_code == 400
        data = response.json()
        
        assert "Configuration file not found" in data["detail"]
    
    def test_create_agent_invalid_config_values(self, client):
        """Test creating agent with invalid configuration values."""
        request_data = {
            "agent_type": "customer_support",
            "config": {
                "escalation_threshold": 1.5,  # Invalid: should be between 0 and 1
                "tools": "invalid_tools_type"  # Invalid: should be list
            }
        }
        
        response = client.post("/agents/create", json=request_data)
        
        assert response.status_code == 400
        data = response.json()
        
        assert "detail" in data
        assert "escalation_threshold must be a number between 0 and 1" in data["detail"]


class TestAgentChatAPI:
    """Test agent chat API endpoints."""
    
    @pytest.fixture
    def created_agent(self, client):
        """Create an agent for testing chat functionality."""
        request_data = {
            "agent_type": "customer_support",
            "name": "test_chat_agent"
        }
        
        response = client.post("/agents/create", json=request_data)
        assert response.status_code == 200
        
        return response.json()["agent_id"]
    
    def test_chat_with_customer_support_agent(self, client, created_agent):
        """Test chatting with customer support agent."""
        chat_request = {
            "message": "I need help with a refund for my recent purchase",
            "context": {
                "customer_id": "cust_123",
                "order_id": "order_456"
            }
        }
        
        response = client.post(f"/agents/{created_agent}/chat", json=chat_request)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "response" in data
        assert data["agent_id"] == created_agent
        assert "metadata" in data
        assert data["metadata"]["agent_type"] == "customer_support"
        assert data["metadata"]["interaction_count"] == 1
    
    def test_chat_with_sales_agent(self, client):
        """Test chatting with sales agent."""
        # First create a sales agent
        create_request = {"agent_type": "sales"}
        create_response = client.post("/agents/create", json=create_request)
        agent_id = create_response.json()["agent_id"]
        
        # Then chat with it
        chat_request = {
            "message": "What are your pricing options for enterprise customers?",
            "context": {
                "customer_id": "enterprise_123",
                "budget": 50000,
                "decision_maker": True
            }
        }
        
        response = client.post(f"/agents/{agent_id}/chat", json=chat_request)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "response" in data
        assert data["agent_id"] == agent_id
        assert "pricing" in data["response"].lower() or "enterprise" in data["response"].lower()
    
    def test_chat_with_research_agent(self, client):
        """Test chatting with research agent."""
        # First create a research agent
        create_request = {"agent_type": "research"}
        create_response = client.post("/agents/create", json=create_request)
        agent_id = create_response.json()["agent_id"]
        
        # Then chat with it
        chat_request = {
            "message": "What are the latest trends in artificial intelligence?",
            "context": {
                "research_type": "trend_analysis",
                "depth": "comprehensive"
            }
        }
        
        response = client.post(f"/agents/{agent_id}/chat", json=chat_request)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "response" in data
        assert data["agent_id"] == agent_id
        assert "artificial intelligence" in data["response"].lower() or "ai" in data["response"].lower()
    
    def test_chat_with_nonexistent_agent(self, client):
        """Test chatting with non-existent agent."""
        chat_request = {
            "message": "Hello",
            "context": {}
        }
        
        response = client.post("/agents/nonexistent_agent_id/chat", json=chat_request)
        
        assert response.status_code == 404
        data = response.json()
        
        assert "Agent nonexistent_agent_id not found" in data["detail"]
    
    def test_multiple_chat_interactions(self, client, created_agent):
        """Test multiple chat interactions with the same agent."""
        messages = [
            "I have a problem with my account",
            "I can't log in to my account",
            "I tried resetting my password but it didn't work"
        ]
        
        for i, message in enumerate(messages, 1):
            chat_request = {
                "message": message,
                "context": {"customer_id": "cust_123"}
            }
            
            response = client.post(f"/agents/{created_agent}/chat", json=chat_request)
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["metadata"]["interaction_count"] == i
    
    def test_chat_empty_message(self, client, created_agent):
        """Test chatting with empty message."""
        chat_request = {
            "message": "",
            "context": {}
        }
        
        response = client.post(f"/agents/{created_agent}/chat", json=chat_request)
        
        # Should still process but might have different behavior
        assert response.status_code in [200, 422]  # Depending on validation


class TestAgentListingAPI:
    """Test agent listing API endpoints."""
    
    def test_list_agents_empty(self, client):
        """Test listing agents when none exist."""
        response = client.get("/agents/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        # Note: There might be agents from other tests, so we don't assert empty
    
    def test_list_agents_with_created_agents(self, client):
        """Test listing agents after creating some."""
        # Create multiple agents
        agent_types = ["customer_support", "sales", "research"]
        created_agents = []
        
        for agent_type in agent_types:
            create_request = {"agent_type": agent_type}
            create_response = client.post("/agents/create", json=create_request)
            assert create_response.status_code == 200
            created_agents.append(create_response.json()["agent_id"])
        
        # List all agents
        response = client.get("/agents/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        assert len(data) >= len(agent_types)
        
        # Check that our created agents are in the list
        agent_ids_in_list = [agent["agent_id"] for agent in data]
        for agent_id in created_agents:
            assert agent_id in agent_ids_in_list
    
    def test_get_specific_agent_info(self, client):
        """Test getting information about a specific agent."""
        # Create an agent
        create_request = {"agent_type": "customer_support", "name": "test_info_agent"}
        create_response = client.post("/agents/create", json=create_request)
        agent_id = create_response.json()["agent_id"]
        
        # Get agent info
        response = client.get(f"/agents/{agent_id}")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["agent_id"] == agent_id
        assert data["agent_type"] == "customer_support"
        assert data["status"] == "active"
        assert isinstance(data["capabilities"], list)
        assert len(data["capabilities"]) > 0
        assert "created_at" in data
    
    def test_get_nonexistent_agent_info(self, client):
        """Test getting information about non-existent agent."""
        response = client.get("/agents/nonexistent_agent_id")
        
        assert response.status_code == 404
        data = response.json()
        
        assert "Agent nonexistent_agent_id not found" in data["detail"]


class TestAgentDeletionAPI:
    """Test agent deletion API endpoints."""

    def test_delete_existing_agent(self, client):
        """Test deleting an existing agent."""
        # Create an agent
        create_request = {"agent_type": "customer_support", "name": "test_delete_agent"}
        create_response = client.post("/agents/create", json=create_request)
        agent_id = create_response.json()["agent_id"]

        # Delete the agent
        response = client.delete(f"/agents/{agent_id}")

        assert response.status_code == 200
        data = response.json()

        assert data["message"] == f"Agent {agent_id} deleted successfully"

        # Verify agent is deleted by trying to get its info
        get_response = client.get(f"/agents/{agent_id}")
        assert get_response.status_code == 404

    def test_delete_nonexistent_agent(self, client):
        """Test deleting non-existent agent."""
        response = client.delete("/agents/nonexistent_agent_id")

        assert response.status_code == 404
        data = response.json()

        assert "Agent nonexistent_agent_id not found" in data["detail"]


class TestAgentHealthAPI:
    """Test agent health check API endpoints."""

    def test_get_agent_health_healthy(self, client):
        """Test getting health status of a healthy agent."""
        # Create an agent
        create_request = {"agent_type": "customer_support"}
        create_response = client.post("/agents/create", json=create_request)
        agent_id = create_response.json()["agent_id"]

        # Get health status
        response = client.get(f"/agents/{agent_id}/health")

        assert response.status_code == 200
        data = response.json()

        assert data["agent_id"] == agent_id
        assert data["status"] == "healthy"
        assert data["name"] == "customer_support"
        assert "uptime" in data
        assert "capabilities_count" in data
        assert data["capabilities_count"] > 0
        assert data["error"] is None

    def test_get_agent_health_nonexistent(self, client):
        """Test getting health status of non-existent agent."""
        response = client.get("/agents/nonexistent_agent_id/health")

        assert response.status_code == 404
        data = response.json()

        assert "Agent nonexistent_agent_id not found" in data["detail"]


class TestAgentConfigAPI:
    """Test agent configuration API endpoints."""

    def test_get_agent_config(self, client):
        """Test getting agent configuration."""
        # Create an agent with custom config
        create_request = {
            "agent_type": "customer_support",
            "config": {
                "escalation_threshold": 0.4,
                "max_resolution_attempts": 5
            }
        }
        create_response = client.post("/agents/create", json=create_request)
        agent_id = create_response.json()["agent_id"]

        # Get configuration
        response = client.get(f"/agents/{agent_id}/config")

        assert response.status_code == 200
        data = response.json()

        assert data["agent_id"] == agent_id
        assert "config" in data
        assert data["config"]["escalation_threshold"] == 0.4
        assert data["config"]["max_resolution_attempts"] == 5
        assert "last_updated" in data

    def test_update_agent_config_merge(self, client):
        """Test updating agent configuration with merge."""
        # Create an agent
        create_request = {
            "agent_type": "sales",
            "config": {
                "qualification_threshold": 0.7,
                "max_discount": 0.15
            }
        }
        create_response = client.post("/agents/create", json=create_request)
        agent_id = create_response.json()["agent_id"]

        # Update configuration (merge)
        update_request = {
            "config": {
                "max_discount": 0.2,
                "new_param": "test_value"
            },
            "merge": True
        }
        response = client.put(f"/agents/{agent_id}/config", json=update_request)

        assert response.status_code == 200
        data = response.json()

        assert data["agent_id"] == agent_id
        assert data["config"]["qualification_threshold"] == 0.7  # Original value preserved
        assert data["config"]["max_discount"] == 0.2  # Updated value
        assert data["config"]["new_param"] == "test_value"  # New value added

    def test_update_agent_config_replace(self, client):
        """Test updating agent configuration with replace."""
        # Create an agent
        create_request = {
            "agent_type": "research",
            "config": {
                "max_sources": 10,
                "fact_check_threshold": 0.8
            }
        }
        create_response = client.post("/agents/create", json=create_request)
        agent_id = create_response.json()["agent_id"]

        # Update configuration (replace)
        update_request = {
            "config": {
                "max_sources": 15,
                "new_param": "replacement_config"
            },
            "merge": False
        }
        response = client.put(f"/agents/{agent_id}/config", json=update_request)

        assert response.status_code == 200
        data = response.json()

        assert data["agent_id"] == agent_id
        assert data["config"]["max_sources"] == 15
        assert data["config"]["new_param"] == "replacement_config"
        # Original fact_check_threshold should be gone since we replaced
        assert "fact_check_threshold" not in data["config"]

    def test_update_config_nonexistent_agent(self, client):
        """Test updating configuration of non-existent agent."""
        update_request = {
            "config": {"test": "value"},
            "merge": True
        }
        response = client.put("/agents/nonexistent_agent_id/config", json=update_request)

        assert response.status_code == 404
        data = response.json()

        assert "Agent nonexistent_agent_id not found" in data["detail"]


class TestBulkAgentCreationAPI:
    """Test bulk agent creation API endpoints."""

    def test_bulk_create_agents_success(self, client):
        """Test successful bulk creation of agents."""
        bulk_request = {
            "agents": [
                {
                    "agent_type": "customer_support",
                    "name": "bulk_support_1",
                    "config": {"escalation_threshold": 0.3}
                },
                {
                    "agent_type": "sales",
                    "name": "bulk_sales_1",
                    "config": {"qualification_threshold": 0.8}
                },
                {
                    "agent_type": "research",
                    "name": "bulk_research_1"
                }
            ]
        }

        response = client.post("/agents/bulk-create", json=bulk_request)

        assert response.status_code == 200
        data = response.json()

        assert isinstance(data, list)
        assert len(data) == 3

        # Check each created agent
        for agent_data in data:
            assert "agent_id" in agent_data
            assert agent_data["status"] == "created"
            assert "capabilities" in agent_data
            assert len(agent_data["capabilities"]) > 0
            assert "created_at" in agent_data

    def test_bulk_create_agents_partial_failure(self, client):
        """Test bulk creation with some failures."""
        bulk_request = {
            "agents": [
                {
                    "agent_type": "customer_support",
                    "name": "bulk_support_2"
                },
                {
                    "agent_type": "invalid_type",  # This should fail
                    "name": "bulk_invalid"
                },
                {
                    "agent_type": "sales",
                    "name": "bulk_sales_2"
                }
            ]
        }

        response = client.post("/agents/bulk-create", json=bulk_request)

        # Should return 207 Multi-Status for partial success
        assert response.status_code == 207
        data = response.json()

        assert "detail" in data
        assert "message" in data["detail"]
        assert "created" in data["detail"]
        assert "failed" in data["detail"]
        assert len(data["detail"]["created"]) == 2  # Two successful
        assert len(data["detail"]["failed"]) == 1   # One failed

    def test_bulk_create_empty_list(self, client):
        """Test bulk creation with empty agent list."""
        bulk_request = {"agents": []}

        response = client.post("/agents/bulk-create", json=bulk_request)

        assert response.status_code == 200
        data = response.json()

        assert isinstance(data, list)
        assert len(data) == 0


class TestMultiAgentCollaborationAPI:
    """Test multi-agent collaboration API endpoints."""

    @pytest.fixture
    def collaboration_agents(self, client):
        """Create multiple agents for collaboration testing."""
        agents = []
        agent_types = ["customer_support", "sales", "research"]

        for agent_type in agent_types:
            create_request = {
                "agent_type": agent_type,
                "name": f"collab_{agent_type}"
            }
            response = client.post("/agents/create", json=create_request)
            assert response.status_code == 200
            agents.append(response.json()["agent_id"])

        return agents

    def test_multi_agent_collaboration_sequential(self, client, collaboration_agents):
        """Test sequential multi-agent collaboration."""
        collab_request = {
            "query": "Help a customer who wants to buy our premium product",
            "agents": collaboration_agents,
            "collaboration_mode": "sequential"
        }

        response = client.post("/agents/multi-agent/collaborate", json=collab_request)

        assert response.status_code == 200
        data = response.json()

        assert "message" in data or "result" in data
        assert "agents" in data
        assert len(data["agents"]) == len(collaboration_agents)

    def test_multi_agent_collaboration_nonexistent_agent(self, client, collaboration_agents):
        """Test collaboration with non-existent agent."""
        invalid_agents = collaboration_agents + ["nonexistent_agent_id"]

        collab_request = {
            "query": "Test collaboration with invalid agent",
            "agents": invalid_agents,
            "collaboration_mode": "sequential"
        }

        response = client.post("/agents/multi-agent/collaborate", json=collab_request)

        assert response.status_code == 404
        data = response.json()

        assert "Agent not found: nonexistent_agent_id" in data["detail"]

    def test_multi_agent_collaboration_empty_agents(self, client):
        """Test collaboration with empty agent list."""
        collab_request = {
            "query": "Test collaboration with no agents",
            "agents": [],
            "collaboration_mode": "sequential"
        }

        response = client.post("/agents/multi-agent/collaborate", json=collab_request)

        # Should handle empty list gracefully
        assert response.status_code in [200, 400]  # Depending on validation


class TestAPIErrorHandling:
    """Test API error handling and edge cases."""

    def test_create_agent_malformed_json(self, client):
        """Test creating agent with malformed JSON."""
        # Send invalid JSON
        response = client.post(
            "/agents/create",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )

        assert response.status_code == 422  # Unprocessable Entity

    def test_create_agent_missing_required_field(self, client):
        """Test creating agent without required agent_type."""
        request_data = {
            "name": "test_agent"
            # Missing agent_type
        }

        response = client.post("/agents/create", json=request_data)

        assert response.status_code == 422
        data = response.json()

        assert "detail" in data
        # Should mention missing agent_type field
        assert any("agent_type" in str(error) for error in data["detail"])

    def test_chat_with_very_long_message(self, client):
        """Test chatting with extremely long message."""
        # Create an agent
        create_request = {"agent_type": "customer_support"}
        create_response = client.post("/agents/create", json=create_request)
        agent_id = create_response.json()["agent_id"]

        # Send very long message
        long_message = "x" * 10000  # 10k characters
        chat_request = {
            "message": long_message,
            "context": {}
        }

        response = client.post(f"/agents/{agent_id}/chat", json=chat_request)

        # Should handle long messages gracefully
        assert response.status_code in [200, 413, 422]  # OK, Payload Too Large, or Validation Error

    def test_concurrent_agent_operations(self, client):
        """Test concurrent operations on the same agent."""
        import threading

        # Create an agent
        create_request = {"agent_type": "customer_support"}
        create_response = client.post("/agents/create", json=create_request)
        agent_id = create_response.json()["agent_id"]

        results = []

        def chat_with_agent(message_id):
            chat_request = {
                "message": f"Concurrent message {message_id}",
                "context": {"message_id": message_id}
            }
            response = client.post(f"/agents/{agent_id}/chat", json=chat_request)
            results.append(response.status_code)

        # Start multiple concurrent requests
        threads = []
        for i in range(5):
            thread = threading.Thread(target=chat_with_agent, args=(i,))
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # All requests should succeed
        assert all(status_code == 200 for status_code in results)
        assert len(results) == 5

    def test_agent_operations_after_deletion(self, client):
        """Test operations on deleted agent."""
        # Create an agent
        create_request = {"agent_type": "sales"}
        create_response = client.post("/agents/create", json=create_request)
        agent_id = create_response.json()["agent_id"]

        # Delete the agent
        delete_response = client.delete(f"/agents/{agent_id}")
        assert delete_response.status_code == 200

        # Try to chat with deleted agent
        chat_request = {
            "message": "Hello deleted agent",
            "context": {}
        }
        chat_response = client.post(f"/agents/{agent_id}/chat", json=chat_request)
        assert chat_response.status_code == 404

        # Try to get info of deleted agent
        info_response = client.get(f"/agents/{agent_id}")
        assert info_response.status_code == 404

        # Try to update config of deleted agent
        config_request = {"config": {"test": "value"}, "merge": True}
        config_response = client.put(f"/agents/{agent_id}/config", json=config_request)
        assert config_response.status_code == 404


class TestVectorDBIntegration:
    """Test VectorDB integration with agent creation and memory."""

    @pytest.fixture
    def vectordb_config(self):
        """Configuration for VectorDB testing."""
        return {
            "memory": {
                "semantic": {
                    "type": "vectordb",
                    "config": {
                        "host": "localhost",
                        "port": 6333,
                        "collection": "test_agent_memory",
                        "api_key": None
                    }
                }
            }
        }

    def test_create_agent_with_vectordb_memory(self, client, vectordb_config):
        """Test creating agent with VectorDB memory configuration."""
        request_data = {
            "agent_type": "customer_support",
            "name": "vectordb_test_agent",
            "config": vectordb_config
        }

        response = client.post("/agent-factory/agents/create", json=request_data)

        assert response.status_code == 200
        data = response.json()

        assert data["agent_type"] == "customer_support"
        assert data["status"] == "created"
        assert "agent_id" in data
        assert "capabilities" in data
        assert len(data["capabilities"]) > 0
        assert "created_at" in data

    def test_agent_memory_persistence_with_vectordb(self, client, vectordb_config):
        """Test that agent memory persists with VectorDB backend."""
        # Create agent with VectorDB memory
        create_request = {
            "agent_type": "customer_support",
            "name": "memory_persistence_agent",
            "config": vectordb_config
        }

        create_response = client.post("/agent-factory/agents/create", json=create_request)
        assert create_response.status_code == 200
        agent_id = create_response.json()["agent_id"]

        # Have multiple conversations to build memory
        conversations = [
            "I need help with my account login issues",
            "My password reset email is not arriving",
            "I've been waiting for 2 hours for the email"
        ]

        for message in conversations:
            chat_request = {
                "message": message,
                "context": {"customer_id": "test_customer_123"}
            }

            chat_response = client.post(f"/agents/{agent_id}/chat", json=chat_request)
            assert chat_response.status_code == 200

        # Final message that should reference previous context
        final_chat_request = {
            "message": "Can you summarize my issue?",
            "context": {"customer_id": "test_customer_123"}
        }

        final_response = client.post(f"/agents/{agent_id}/chat", json=final_chat_request)
        assert final_response.status_code == 200

        # Response should reference previous conversation context
        response_text = final_response.json()["response"].lower()
        assert any(keyword in response_text for keyword in ["login", "password", "email", "account"])

    def test_multiple_agents_separate_vectordb_collections(self, client):
        """Test that multiple agents maintain separate VectorDB collections."""
        agents_config = [
            {
                "agent_type": "customer_support",
                "name": "support_agent_1",
                "config": {
                    "memory": {
                        "semantic": {
                            "type": "vectordb",
                            "config": {
                                "host": "localhost",
                                "port": 6333,
                                "collection": "support_agent_1_memory",
                                "api_key": None
                            }
                        }
                    }
                }
            },
            {
                "agent_type": "sales",
                "name": "sales_agent_1",
                "config": {
                    "memory": {
                        "semantic": {
                            "type": "vectordb",
                            "config": {
                                "host": "localhost",
                                "port": 6333,
                                "collection": "sales_agent_1_memory",
                                "api_key": None
                            }
                        }
                    }
                }
            }
        ]

        created_agents = []

        # Create both agents
        for agent_config in agents_config:
            response = client.post("/agent-factory/agents/create", json=agent_config)
            assert response.status_code == 200
            created_agents.append(response.json()["agent_id"])

        # Have different conversations with each agent
        support_message = "I have a technical issue with the product"
        sales_message = "I'm interested in your enterprise pricing"

        # Chat with support agent
        support_chat = {
            "message": support_message,
            "context": {"customer_id": "support_customer"}
        }
        support_response = client.post(f"/agents/{created_agents[0]}/chat", json=support_chat)
        assert support_response.status_code == 200

        # Chat with sales agent
        sales_chat = {
            "message": sales_message,
            "context": {"customer_id": "sales_customer"}
        }
        sales_response = client.post(f"/agents/{created_agents[1]}/chat", json=sales_chat)
        assert sales_response.status_code == 200

        # Verify responses are contextually appropriate
        support_response_text = support_response.json()["response"].lower()
        sales_response_text = sales_response.json()["response"].lower()

        # Support agent should focus on technical help
        assert any(keyword in support_response_text for keyword in ["technical", "issue", "help", "support"])

        # Sales agent should focus on pricing/sales
        assert any(keyword in sales_response_text for keyword in ["pricing", "enterprise", "sales", "cost"])

    def test_vectordb_error_handling(self, client):
        """Test error handling when VectorDB is misconfigured."""
        # Configuration with invalid VectorDB settings
        invalid_config = {
            "memory": {
                "semantic": {
                    "type": "vectordb",
                    "config": {
                        "host": "invalid_host",
                        "port": 9999,  # Invalid port
                        "collection": "test_collection",
                        "api_key": None
                    }
                }
            }
        }

        request_data = {
            "agent_type": "customer_support",
            "name": "invalid_vectordb_agent",
            "config": invalid_config
        }

        # Should handle VectorDB connection errors gracefully
        response = client.post("/agent-factory/agents/create", json=request_data)

        # Depending on implementation, this might succeed with fallback or fail gracefully
        assert response.status_code in [200, 400, 500]

        if response.status_code != 200:
            data = response.json()
            assert "detail" in data
            # Should provide meaningful error message
            assert any(keyword in data["detail"].lower() for keyword in ["connection", "vectordb", "database", "error"])

    def test_vectordb_memory_search_functionality(self, client, vectordb_config):
        """Test VectorDB memory search functionality through agent interactions."""
        # Create agent with VectorDB memory
        create_request = {
            "agent_type": "research",
            "name": "research_memory_agent",
            "config": vectordb_config
        }

        create_response = client.post("/agent-factory/agents/create", json=create_request)
        assert create_response.status_code == 200
        agent_id = create_response.json()["agent_id"]

        # Add diverse information to memory through conversations
        research_topics = [
            "What are the latest trends in artificial intelligence?",
            "How does machine learning impact healthcare?",
            "What are the benefits of cloud computing for businesses?",
            "Explain the concept of blockchain technology",
            "What is the future of renewable energy?"
        ]

        # Have conversations on different topics
        for topic in research_topics:
            chat_request = {
                "message": topic,
                "context": {"research_session": "memory_test"}
            }

            response = client.post(f"/agents/{agent_id}/chat", json=chat_request)
            assert response.status_code == 200

        # Ask a question that should trigger memory search
        memory_search_request = {
            "message": "What did we discuss about AI and healthcare earlier?",
            "context": {"research_session": "memory_test"}
        }

        search_response = client.post(f"/agents/{agent_id}/chat", json=search_response)
        assert search_response.status_code == 200

        # Response should reference previous conversations
        response_text = search_response.json()["response"].lower()
        assert any(keyword in response_text for keyword in ["artificial intelligence", "machine learning", "healthcare", "ai"])

    def test_vectordb_collection_management(self, client):
        """Test VectorDB collection management through agent lifecycle."""
        collection_name = "lifecycle_test_collection"

        # Create agent with specific collection
        create_request = {
            "agent_type": "customer_support",
            "name": "lifecycle_test_agent",
            "config": {
                "memory": {
                    "semantic": {
                        "type": "vectordb",
                        "config": {
                            "host": "localhost",
                            "port": 6333,
                            "collection": collection_name,
                            "api_key": None
                        }
                    }
                }
            }
        }

        create_response = client.post("/agent-factory/agents/create", json=create_request)
        assert create_response.status_code == 200
        agent_id = create_response.json()["agent_id"]

        # Add some memory through conversation
        chat_request = {
            "message": "This is a test message for collection management",
            "context": {"test": "collection_lifecycle"}
        }

        chat_response = client.post(f"/agents/{agent_id}/chat", json=chat_request)
        assert chat_response.status_code == 200

        # Delete the agent (should handle collection cleanup gracefully)
        delete_response = client.delete(f"/agents/{agent_id}")

        # Should succeed or handle gracefully
        assert delete_response.status_code in [200, 404]  # 404 if agent not found is acceptable

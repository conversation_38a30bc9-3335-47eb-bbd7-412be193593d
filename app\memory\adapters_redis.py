"""
Redis Storage Adapter for AgentKit Memory

Provides scalable, distributed storage for short-term and episodic memory.
"""
import redis
import json
import logging
from typing import Any, List, Optional
from .adapters import BaseMemoryAdapter

logger = logging.getLogger(__name__)


class RedisAdapter(BaseMemoryAdapter):
    """
    Redis storage adapter for scalable, distributed memory.

    Provides high-performance, distributed storage suitable for production
    environments with multiple agent instances.
    """

    def __init__(self, host: str = "localhost", port: int = 6379, db: int = 0, **kwargs):
        """
        Initialize the Redis adapter.

        Args:
            host: Redis server host
            port: Redis server port
            db: Redis database number
            **kwargs: Additional Redis connection parameters
        """
        try:
            self.client = redis.Redis(
                host=host,
                port=port,
                db=db,
                decode_responses=False,  # We handle JSON encoding/decoding
                **kwargs
            )
            # Test connection
            self.client.ping()
            logger.info(f"RedisAdapter initialized: {host}:{port}/{db}")
        except Exception as e:
            logger.error(f"Failed to initialize Redis connection: {e}")
            raise

    def add(self, key: str, item: Any) -> None:
        """
        Add an item to Redis list.

        Args:
            key: Storage key
            item: Item to store
        """
        try:
            serialized_item = json.dumps(item)
            self.client.rpush(key, serialized_item)
            logger.debug(f"Added item to Redis storage: {key}")
        except Exception as e:
            logger.error(f"Failed to add item to Redis storage: {e}")
            raise

    def get(self, key: str, limit: Optional[int] = None) -> List[Any]:
        """
        Retrieve items from Redis list.

        Args:
            key: Storage key
            limit: Maximum number of items to retrieve

        Returns:
            List of stored items
        """
        try:
            if limit:
                # Get the last 'limit' items
                items = self.client.lrange(key, -limit, -1)
            else:
                # Get all items
                items = self.client.lrange(key, 0, -1)

            result = []
            for item in items:
                try:
                    deserialized_item = json.loads(item)
                    result.append(deserialized_item)
                except json.JSONDecodeError as e:
                    logger.warning(f"Skipping invalid JSON item in Redis: {e}")
                    continue

            logger.debug(f"Retrieved {len(result)} items from Redis storage: {key}")
            return result

        except Exception as e:
            logger.error(f"Failed to retrieve items from Redis storage: {e}")
            return []

    def clear(self, key: str) -> None:
        """
        Clear all items for a key from Redis.

        Args:
            key: Storage key to clear
        """
        try:
            self.client.delete(key)
            logger.debug(f"Cleared Redis storage: {key}")
        except Exception as e:
            logger.error(f"Failed to clear Redis storage: {e}")
            raise

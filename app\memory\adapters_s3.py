"""
S3Adapter for agent memory storage.
"""
from typing import Any, Dict, Optional
import boto3
import json

class S3Adapter:
    def __init__(self, bucket: str, prefix: str = "agent-memory/"):
        self.s3 = boto3.client("s3")
        self.bucket = bucket
        self.prefix = prefix

    def _key(self, key: str) -> str:
        return f"{self.prefix}{key}.json"

    def save(self, key: str, value: Dict[str, Any]):
        self.s3.put_object(Bucket=self.bucket, Key=self._key(key), Body=json.dumps(value))

    def load(self, key: str) -> Optional[Dict[str, Any]]:
        try:
            obj = self.s3.get_object(Bucket=self.bucket, Key=self._key(key))
            return json.loads(obj["Body"].read())
        except self.s3.exceptions.NoSuchKey:
            return None

    def delete(self, key: str):
        self.s3.delete_object(Bucket=self.bucket, Key=self._key(key))

    def list_keys(self):
        response = self.s3.list_objects_v2(Bucket=self.bucket, Prefix=self.prefix)
        return [item["Key"][len(self.prefix):-5] for item in response.get("Contents", [])]

"""
Core exceptions for the Agentic AI Framework.

This module defines custom exceptions used throughout the framework
to provide clear error handling and debugging information.
"""

from typing import Optional, Any, Dict


class AgentKitError(Exception):
    """Base exception for all AgentKit errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}


class AgentError(AgentKitError):
    """Raised when agent operations fail."""
    pass


class ConfigurationError(AgentKitError):
    """Raised when configuration is invalid or missing."""
    pass


class FactoryError(AgentKitError):
    """Raised when agent factory operations fail."""
    pass


class PlannerError(AgentKitError):
    """Raised when planner operations fail."""
    pass


class ToolError(AgentKitError):
    """Raised when tool operations fail."""
    pass


class MemoryError(AgentKitError):
    """Raised when memory operations fail."""
    pass


class ValidationError(AgentKitError):
    """Raised when validation operations fail."""
    pass


class PolicyLoadError(AgentKitError):
    """Raised when policy loading fails."""
    pass


class ProviderLoadError(AgentKitError):
    """Raised when LLM provider loading fails."""
    pass
"""
MongoDBAdapter for agent memory storage.
"""
from typing import Any, Dict, Optional
from pymongo import MongoClient

class MongoDBAdapter:
    def __init__(self, uri: str, db_name: str, collection: str):
        self.client = MongoClient(uri)
        self.db = self.client[db_name]
        self.collection = self.db[collection]

    def save(self, key: str, value: Dict[str, Any]):
        self.collection.update_one({"_id": key}, {"$set": value}, upsert=True)

    def load(self, key: str) -> Optional[Dict[str, Any]]:
        return self.collection.find_one({"_id": key})

    def delete(self, key: str):
        self.collection.delete_one({"_id": key})

    def list_keys(self):
        return [doc["_id"] for doc in self.collection.find({}, {"_id": 1})]

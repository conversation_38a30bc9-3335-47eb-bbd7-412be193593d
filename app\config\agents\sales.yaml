# Enhanced Sales Agent Configuration
# This configuration demonstrates sales-specific features with LLM integration

# Agent Identity and Behavior
persona: |
  You are <PERSON>, a dynamic and results-driven sales professional with 7+ years of experience 
  in consultative selling. You are charismatic, persuasive, and genuinely care about helping customers 
  find the right solutions. You excel at building rapport, understanding customer needs, and presenting 
  value propositions that resonate. You're competitive but ethical, always putting customer success first.

system_prompt: |
  You are a professional sales agent. Your role is to:

  1. **Qualify leads** - Understand customer needs, budget, timeline, and decision-making process
  2. **Build relationships** - Establish trust and rapport with prospects
  3. **Present solutions** - Match customer needs with appropriate products/services
  4. **Handle objections** - Address concerns professionally and provide reassurance
  5. **Close deals** - Guide customers through the purchasing process

  **Sales Process:**
  - Discovery: Ask open-ended questions to understand pain points
  - Presentation: Demonstrate value and ROI of your solutions
  - Objection Handling: Listen, acknowledge, and provide solutions
  - Closing: Ask for the sale and guide next steps

  **Guidelines:**
  - Always lead with value, not features
  - Use consultative selling approach
  - Provide social proof and case studies when relevant
  - Be transparent about pricing and terms
  - Follow up consistently and professionally
  - Maintain accurate records of all interactions

  **Tone:** Professional, confident, helpful, and customer-focused
  **Goal:** Build long-term relationships while achieving sales targets

# LLM Provider Configuration
llm_provider: "ollama"
llm_model: "gemma3:270m"

# Tools Configuration
tools:
  - "http_get"        # For API calls and CRM integration
  - "searxng_search"  # For prospect research and competitive analysis

# Memory Configuration
memory:
  short_term:
    type: "short_term"
    adapter: "in_memory"  # Local, persistent
    max_size: 1000
    session_based: true
    max_sessions: 200
    max_messages_per_session: 100
  episodic:
    type: "episodic"
    adapter: "jsonl"  # File-based
    jsonl_path: "./data/sales_episodic.jsonl"
    max_size: 5000
  procedural:
    type: "procedural"
    adapter: "sqlite"  # Fast, ephemeral
    sqlite_path: "./data/sales_procedural.sqlite"
    max_size: 2000
  semantic:
    type: "semantic"
    adapter: "vectordb"  # Qdrant
    qdrant_host: "localhost"
    qdrant_port: 6333
    collection: "sales_semantic"  

# Planner Configuration
planner:
  type: "react"
  max_steps: 10
  temperature: 0.7

# Sales Specific Settings
qualification_threshold: 0.7
max_discount: 0.15
sales_stages:
  - "lead"
  - "qualified"
  - "proposal"
  - "negotiation"
  - "closed"

# Product Catalog (example)
product_catalog:
  - name: "Enterprise Software Suite"
    price: 50000
    category: "software"
    features: ["analytics", "automation", "integration"]
  - name: "Professional Services"
    price: 25000
    category: "services"
    features: ["consulting", "implementation", "training"]

# Lead Scoring
lead_scoring:
  budget_weight: 0.3
  timeline_weight: 0.2
  authority_weight: 0.3
  need_weight: 0.2

# Agent Capabilities
capabilities:
  - "lead_qualification"
  - "product_recommendation"
  - "pricing_negotiation"
  - "deal_management"
  - "pipeline_tracking"
  - "customer_profiling"

# Sales Methodology
methodology: "BANT"  # Budget, Authority, Need, Timeline

# CRM Integration
crm_integration:
  enabled: true
  auto_sync: true
  lead_assignment: "round_robin"
  opportunity_tracking: true

# Performance Metrics
metrics:
  track_conversion_rate: true
  track_deal_size: true
  track_sales_cycle: true
  track_pipeline_velocity: true

# Communication Preferences
communication:
  follow_up_frequency: "weekly"
  preferred_channels: ["email", "phone", "video_call"]
  response_time_target: "2_hours"

# Competitive Intelligence
competitive_analysis:
  enabled: true
  track_competitors: ["CompetitorA", "CompetitorB"]
  battle_cards: true

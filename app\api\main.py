# main.py
# app/api/main.py

from fastapi import <PERSON><PERSON><PERSON>, APIRouter
from fastapi.middleware.cors import CORSMiddleware
from app.api.routes import agents

app = FastAPI(
    title="Agentic AI Framework",
    description="Configurable Agents with Planning, Tools, MCP, Memory, Validation, Policies, and Observability",
    version="1.0.0"
)

# Enable CORS (optional for UI integrations)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For local dev; restrict for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Unified API Router
api_router = APIRouter()
api_router.include_router(agents.router, prefix="/agent-factory", tags=["Agent Factory"])

# Register the unified router
app.include_router(api_router)



"""
Memory Storage Adapters for AgentKit

Provides pluggable backends for scaling memory types: in-memory, file, database, cloud, vector DB.
"""
from abc import ABC, abstractmethod
from typing import Any, List, Dict, Optional
import logging
import time
from ..core.types import Message

logger = logging.getLogger(__name__)


class BaseMemoryAdapter(ABC):
    """
    Abstract base class for all memory adapters.

    Provides consistent interface for memory operations across different storage backends.
    All adapters must implement these methods to ensure compatibility.
    """

    @abstractmethod
    def add(self, key: str, item: Any) -> None:
        """Add an item to storage."""
        pass

    @abstractmethod
    def get(self, key: str, limit: Optional[int] = None) -> List[Any]:
        """Retrieve items from storage."""
        pass

    @abstractmethod
    def clear(self, key: str) -> None:
        """Clear all items for a key."""
        pass

    def add_message(self, agent_id: str, user_id: str, session_id: str, message: Message) -> None:
        """
        Add a message to memory with proper context.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            session_id: Session identifier
            message: Message object to store
        """
        key = self._build_key(agent_id, user_id, session_id)
        message_data = {
            "role": message.role,
            "content": message.content,
            "timestamp": time.time(),
            "agent_id": agent_id,
            "user_id": user_id,
            "session_id": session_id
        }

        try:
            self.add(key, message_data)
            logger.debug(f"Added message to memory: {key}")
        except Exception as e:
            logger.error(f"Failed to add message to memory: {e}")
            raise

    def get_messages(self, agent_id: str, user_id: str, session_id: str, limit: Optional[int] = None) -> List[Message]:
        """
        Retrieve messages from memory with proper context.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            session_id: Session identifier
            limit: Maximum number of messages to retrieve

        Returns:
            List of Message objects
        """
        key = self._build_key(agent_id, user_id, session_id)

        try:
            items = self.get(key, limit)
            messages = []
            for item in items:
                if isinstance(item, dict) and "role" in item and "content" in item:
                    messages.append(Message(role=item["role"], content=item["content"]))

            logger.debug(f"Retrieved {len(messages)} messages from memory: {key}")
            return messages
        except Exception as e:
            logger.error(f"Failed to retrieve messages from memory: {e}")
            return []

    def log_event(self, event: Dict[str, Any]) -> None:
        """
        Log an event to episodic memory.

        Args:
            event: Event data to log
        """
        agent_id = event.get("agent_id", "unknown")
        user_id = event.get("user_id", "unknown")
        session_id = event.get("session_id", "default")

        key = self._build_key(agent_id, user_id, session_id, "episodic")
        event_data = {
            **event,
            "timestamp": event.get("timestamp", time.time()),
            "event_type": "episodic"
        }

        try:
            self.add(key, event_data)
            logger.debug(f"Logged event to episodic memory: {key}")
        except Exception as e:
            logger.error(f"Failed to log event to episodic memory: {e}")
            raise

    def add_procedure(self, agent_id: str, user_id: str, procedure: Dict[str, Any]) -> None:
        """
        Add a procedure to procedural memory.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            procedure: Procedure data to store
        """
        key = self._build_key(agent_id, user_id, None, "procedural")
        procedure_data = {
            **procedure,
            "timestamp": time.time(),
            "agent_id": agent_id,
            "user_id": user_id,
            "memory_type": "procedural"
        }

        try:
            self.add(key, procedure_data)
            logger.debug(f"Added procedure to memory: {key}")
        except Exception as e:
            logger.error(f"Failed to add procedure to memory: {e}")
            raise

    def add_fact(self, agent_id: str, user_id: str, fact: Dict[str, Any]) -> None:
        """
        Add a fact to semantic memory.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            fact: Fact data to store
        """
        key = self._build_key(agent_id, user_id, None, "semantic")
        fact_data = {
            **fact,
            "timestamp": time.time(),
            "agent_id": agent_id,
            "user_id": user_id,
            "memory_type": "semantic"
        }

        try:
            self.add(key, fact_data)
            logger.debug(f"Added fact to semantic memory: {key}")
        except Exception as e:
            logger.error(f"Failed to add fact to semantic memory: {e}")
            raise

    def get_messages(self, agent_id: str, user_id: str, session_id: str, limit: Optional[int] = None) -> List[Any]:
        """
        Get messages from short-term memory.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            session_id: Session identifier
            limit: Maximum number of messages to retrieve

        Returns:
            List of messages
        """
        key = self._build_key(agent_id, user_id, session_id)
        try:
            messages = self.get(key, limit)
            logger.debug(f"Retrieved {len(messages)} messages from memory: {key}")
            return messages
        except Exception as e:
            logger.error(f"Failed to retrieve messages from memory: {e}")
            return []

    def _build_key(self, agent_id: str, user_id: str, session_id: Optional[str] = None, memory_type: Optional[str] = None) -> str:
        """
        Build a consistent key for memory storage.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            session_id: Session identifier (optional)
            memory_type: Type of memory (optional)

        Returns:
            Formatted key string
        """
        parts = [agent_id, user_id]
        if session_id:
            parts.append(session_id)
        if memory_type:
            parts.append(memory_type)

        return ":".join(parts)

class InMemoryAdapter(BaseMemoryAdapter):
    """
    In-memory storage adapter for short-term and episodic memory.

    Fast, ephemeral storage suitable for development and testing.
    Data is lost when the process restarts.
    """

    def __init__(self):
        """Initialize the in-memory storage."""
        self.data: Dict[str, List[Any]] = {}
        logger.info("InMemoryAdapter initialized")

    def add(self, key: str, item: Any) -> None:
        """
        Add an item to memory.

        Args:
            key: Storage key
            item: Item to store
        """
        try:
            self.data.setdefault(key, []).append(item)
            logger.debug(f"Added item to in-memory storage: {key}")
        except Exception as e:
            logger.error(f"Failed to add item to in-memory storage: {e}")
            raise

    def get(self, key: str, limit: Optional[int] = None) -> List[Any]:
        """
        Retrieve items from memory.

        Args:
            key: Storage key
            limit: Maximum number of items to retrieve

        Returns:
            List of stored items
        """
        try:
            items = self.data.get(key, [])
            result = items[-limit:] if limit else items
            logger.debug(f"Retrieved {len(result)} items from in-memory storage: {key}")
            return result
        except Exception as e:
            logger.error(f"Failed to retrieve items from in-memory storage: {e}")
            return []

    def clear(self, key: str) -> None:
        """
        Clear all items for a key.

        Args:
            key: Storage key to clear
        """
        try:
            self.data[key] = []
            logger.debug(f"Cleared in-memory storage: {key}")
        except Exception as e:
            logger.error(f"Failed to clear in-memory storage: {e}")
            raise

class JSONLAdapter(BaseMemoryAdapter):
    """
    File-based JSONL adapter for episodic memory (append-only log).

    Persistent storage using JSON Lines format, suitable for episodic memory
    where events need to be preserved across restarts.
    """

    def __init__(self, file_path: str):
        """
        Initialize the JSONL adapter.

        Args:
            file_path: Path to the JSONL file
        """
        self.file_path = file_path
        self._ensure_file_exists()
        logger.info(f"JSONLAdapter initialized with file: {file_path}")

    def _ensure_file_exists(self) -> None:
        """Ensure the JSONL file exists."""
        import os
        try:
            if not os.path.exists(self.file_path):
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
                # Create empty file
                with open(self.file_path, "w", encoding="utf-8") as f:
                    pass
        except Exception as e:
            logger.error(f"Failed to create JSONL file: {e}")
            raise

    def add(self, key: str, item: Any) -> None:
        """
        Add an item to the JSONL file.

        Args:
            key: Storage key
            item: Item to store
        """
        import json
        try:
            # Ensure item is a dictionary
            if not isinstance(item, dict):
                item = {"data": item}

            record = {"key": key, **item}

            with open(self.file_path, "a", encoding="utf-8") as f:
                f.write(json.dumps(record) + "\n")

            logger.debug(f"Added item to JSONL storage: {key}")
        except Exception as e:
            logger.error(f"Failed to add item to JSONL storage: {e}")
            raise

    def get(self, key: str, limit: Optional[int] = None) -> List[Any]:
        """
        Retrieve items from the JSONL file.

        Args:
            key: Storage key
            limit: Maximum number of items to retrieve

        Returns:
            List of stored items
        """
        import json
        try:
            items = []

            with open(self.file_path, "r", encoding="utf-8") as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        obj = json.loads(line)
                        if obj.get("key") == key:
                            # Remove the key field from the returned item
                            item = {k: v for k, v in obj.items() if k != "key"}
                            items.append(item)
                    except json.JSONDecodeError as e:
                        logger.warning(f"Skipping invalid JSON line: {e}")
                        continue

            result = items[-limit:] if limit else items
            logger.debug(f"Retrieved {len(result)} items from JSONL storage: {key}")
            return result

        except FileNotFoundError:
            logger.warning(f"JSONL file not found: {self.file_path}")
            return []
        except Exception as e:
            logger.error(f"Failed to retrieve items from JSONL storage: {e}")
            return []

    def clear(self, key: str) -> None:
        """
        Clear all items for a key from the JSONL file.

        Args:
            key: Storage key to clear
        """
        import json
        try:
            items = []

            # Read all items except those with the specified key
            with open(self.file_path, "r", encoding="utf-8") as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        obj = json.loads(line)
                        if obj.get("key") != key:
                            items.append(obj)
                    except json.JSONDecodeError as e:
                        logger.warning(f"Skipping invalid JSON line during clear: {e}")
                        continue

            # Write back all items except the cleared ones
            with open(self.file_path, "w", encoding="utf-8") as f:
                for obj in items:
                    f.write(json.dumps(obj) + "\n")

            logger.debug(f"Cleared JSONL storage: {key}")

        except Exception as e:
            logger.error(f"Failed to clear JSONL storage: {e}")
            raise

# Add more adapters: SQLiteAdapter, RedisAdapter, VectorDBAdapter, etc.

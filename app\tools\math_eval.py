import ast, operator, math
from .base import ToolSpec, Tool

_ops = {
    ast.Add: operator.add, ast.Sub: operator.sub, ast.Mult: operator.mul,
    ast.Div: operator.truediv, ast.Pow: operator.pow, ast.Mod: operator.mod
}
_unary = {ast.UAdd: lambda x:x, ast.USub: operator.neg}

def _eval_expr(node):
    if isinstance(node, ast.Num): return node.n
    if isinstance(node, ast.Constant) and isinstance(node.value, (int, float)): return node.value
    if isinstance(node, ast.BinOp) and type(node.op) in _ops:
        return _ops[type(node.op)](_eval_expr(node.left), _eval_expr(node.right))
    if isinstance(node, ast.UnaryOp) and type(node.op) in _unary:
        return _unary[type(node.op)](_eval_expr(node.operand))
    if isinstance(node, ast.Name) and node.id in {"pi", "e"}:
        return getattr(math, node.id)
    raise ValueError("Unsupported expression")

def _math_eval(params):
    expr = params["expr"]
    node = ast.parse(expr, mode="eval").body
    return {"expr": expr, "value": float(_eval_expr(node))}

math_eval = Tool(
    ToolSpec(
        name="math.eval",
        description="Evaluate a simple arithmetic expression safely",
        input_schema={"type": "object", "properties": {"expr": {"type": "string"}}, "required": ["expr"]},
        output_schema={"type": "object"},
    ),
    _math_eval,
)

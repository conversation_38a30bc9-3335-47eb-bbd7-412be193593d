[build-system]
requires = ["setuptools>=68", "wheel"]
build-backend = "setuptools.build_meta"
[tool.setuptools]
packages = ["app"]

[project]
name = "agentkit"
version = "0.1.0"
description = "Modular Agentic AI starter framework (planner, tools, memory, guards, interface)."
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
  "fastapi>=0.115.0",
  "uvicorn[standard]>=0.30.0",
  "pydantic>=2.7.0",
  "pydantic-settings>=2.2.1",
  "httpx>=0.27.0",
  "PyYAML>=6.0.1",
  "loguru>=0.7.2",
  "rich>=13.7.1",
  "typing-extensions>=4.12.0",
  "qdrant-client>=1.7.0",
  "redis>=5.0.4",
  "pymongo>=4.7.2",
  "psycopg2>=2.9.9",
  "boto3>=1.34.0",
]

[project.optional-dependencies]
openai = ["openai>=1.40.0"]
mistral = ["mistralai>=0.4.0"]
gemini = ["google-generativeai>=0.7.2"]
zhipu = ["zhipuai>=2.0.0"]
ollama = ["ollama>=0.3.0"]

[tool.pytest.ini_options]
testpaths = ["tests"]
addopts = "-q"

from __future__ import annotations
import os
from typing import Literal

from .base import LLM

ProviderName = Literal["openai", "mistral", "gemini", "zhipu","ollama"]

class ProviderLoadError(RuntimeError):
    pass

def _load_ollama() -> LLM:
    try:
        from .ollama_provider import OllamaProvider
    except Exception as e:
        raise ProviderLoadError(
            "Ollama provider not available. Install: pip install 'ollama>=0.3.0'"
        ) from e
    # No API key; ensure daemon reachable is handled in provider __init__
    return OllamaProvider(model_env="OLLAMA_MODEL", host_env="OLLAMA_HOST")

def _load_openai() -> LLM:
    try:
        from .openai_provider import OpenAIProvider
    except Exception as e:
        raise ProviderLoadError(
            "OpenAI provider not available. Install extras: pip install '.[openai]'"
        ) from e
    if not os.getenv("OPENAI_API_KEY"):
        raise ProviderLoadError("OPENAI_API_KEY is not set in environment.")
    return OpenAIProvider(model_env="LLM_MODEL")  # uses OPENAI_API_KEY internally

def _load_mistral() -> LLM:
    try:
        from .mistral_provider import MistralProvider
    except Exception as e:
        raise ProviderLoadError(
            "Mistral provider not available. Install: pip install 'mistralai>=0.4.0'"
        ) from e
    if not os.getenv("MISTRAL_API_KEY"):
        raise ProviderLoadError("MISTRAL_API_KEY is not set in environment.")
    return MistralProvider(model_env="MISTRAL_MODEL", api_key_env="MISTRAL_API_KEY")

def _load_gemini() -> LLM:
    try:
        from .gemini_provider import GeminiProvider
    except Exception as e:
        raise ProviderLoadError(
            "Gemini provider not available. Install: pip install 'google-generativeai>=0.7.2'"
        ) from e
    if not os.getenv("GEMINI_API_KEY"):
        raise ProviderLoadError("GEMINI_API_KEY is not set in environment.")
    return GeminiProvider(model_env="GEMINI_MODEL", api_key_env="GEMINI_API_KEY")

def _load_zhipu() -> LLM:
    try:
        from .zhipu_provider import ZhipuProvider
    except Exception as e:
        raise ProviderLoadError(
            "Zhipu provider not available. Install: pip install 'zhipuai>=2.0.0'"
        ) from e
    if not os.getenv("ZHIPUAI_API_KEY"):
        raise ProviderLoadError("ZHIPUAI_API_KEY is not set in environment.")
    return ZhipuProvider(model_env="ZHIPUAI_MODEL", api_key_env="ZHIPUAI_API_KEY")

_LOADERS: dict[str, callable] = {
    "openai": _load_openai,
    "mistral": _load_mistral,
    "gemini": _load_gemini,
    "zhipu": _load_zhipu,
    "ollama": _load_ollama,
}

def get_supported_providers() -> list[str]:
    return sorted(_LOADERS.keys())

def get_llm(provider: ProviderName | None = None) -> LLM:
    """
    Returns an LLM instance based on env var LLM_PROVIDER or provided name.
    """
    name = (provider or os.getenv("LLM_PROVIDER", "openai")).lower()
    if name not in _LOADERS:
        raise ProviderLoadError(
            f"Unknown LLM_PROVIDER='{name}'. Supported: {', '.join(get_supported_providers())}"
        )
    return _LOADERS[name]()

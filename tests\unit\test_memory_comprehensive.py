"""
Comprehensive unit tests for memory system architecture.

This module contains comprehensive unit tests for all memory components
including adapters, memory types, and memory manager integration.
"""
import pytest
import tempfile
import os
import time
from unittest.mock import Mock, patch, MagicMock

from app.memory.adapters import <PERSON><PERSON><PERSON>ory<PERSON>dapter, InMemoryAdapter, JSONLAdapter
from app.memory.adapters_sqlite import SQLiteAdapter
from app.memory.adapters_redis import RedisAdapter
from app.memory.base import (
    ShortTermMemory, EpisodicMemory, ProceduralMemory, SemanticMemory,
    AgentMemory, MemoryManager
)
from app.core.types import Message


class TestBaseMemoryAdapter:
    """Test BaseMemoryAdapter interface methods."""
    
    def test_base_adapter_interface(self):
        """Test that BaseMemoryAdapter provides proper interface methods."""
        adapter = InMemoryAdapter()
        
        # Test add_message method
        message = Message(role="user", content="Hello")
        adapter.add_message("agent1", "user1", "session1", message)
        
        # Test get_messages method
        messages = adapter.get_messages("agent1", "user1", "session1")
        assert len(messages) == 1
        assert messages[0]["content"] == "Hello"
        
        # Test log_event method
        event = {"action": "test", "result": "success"}
        adapter.log_event(event)
        
        # Test add_procedure method
        procedure = {"name": "test_proc", "steps": ["step1", "step2"]}
        adapter.add_procedure("agent1", "user1", procedure)
        
        # Test add_fact method
        fact = {"concept": "test", "value": "fact"}
        adapter.add_fact("agent1", "user1", fact)


class TestInMemoryAdapter:
    """Test InMemoryAdapter functionality."""
    
    def test_add_and_get(self):
        """Test adding and retrieving items."""
        adapter = InMemoryAdapter()
        
        # Test adding items
        adapter.add("test_key", {"data": "test_value"})
        adapter.add("test_key", {"data": "test_value2"})
        
        # Test retrieving items
        items = adapter.get("test_key")
        assert len(items) == 2
        assert items[0]["data"] == "test_value"
        assert items[1]["data"] == "test_value2"
    
    def test_message_operations(self):
        """Test message-specific operations."""
        adapter = InMemoryAdapter()
        message = Message(role="user", content="Test message")
        
        # Add message
        adapter.add_message("agent1", "user1", "session1", message)
        
        # Get messages
        messages = adapter.get_messages("agent1", "user1", "session1")
        assert len(messages) == 1
        assert messages[0]["content"] == "Test message"
        assert messages[0]["role"] == "user"
        assert "timestamp" in messages[0]


class TestShortTermMemory:
    """Test ShortTermMemory functionality."""
    
    def test_memory_without_adapter(self):
        """Test short-term memory without adapter (in-memory fallback)."""
        memory = ShortTermMemory("agent1", "user1", "session1")
        message = Message(role="user", content="Hello")
        
        # Add message
        memory.add_message("agent1", "user1", "session1", message)
        
        # Get messages
        messages = memory.get_messages("agent1", "user1", "session1")
        assert len(messages) == 1
        assert messages[0].content == "Hello"
    
    def test_memory_with_adapter(self):
        """Test short-term memory with adapter."""
        adapter = InMemoryAdapter()
        memory = ShortTermMemory("agent1", "user1", "session1", adapter=adapter)
        message = Message(role="user", content="Hello with adapter")
        
        # Add message
        memory.add_message("agent1", "user1", "session1", message)
        
        # Get messages
        messages = memory.get_messages("agent1", "user1", "session1")
        assert len(messages) == 1
        assert messages[0]["content"] == "Hello with adapter"


class TestEpisodicMemory:
    """Test EpisodicMemory functionality."""
    
    def test_memory_without_adapter(self):
        """Test episodic memory without adapter (in-memory fallback)."""
        memory = EpisodicMemory("agent1", "user1", "session1")
        event = {"action": "test_action", "result": "success"}
        
        # Log event
        memory.log_event(event)
        
        # Get episodes
        episodes = memory.get_episodes()
        assert len(episodes) == 1
        assert episodes[0]["action"] == "test_action"
        assert "timestamp" in episodes[0]
    
    def test_memory_with_adapter(self):
        """Test episodic memory with adapter."""
        adapter = InMemoryAdapter()
        memory = EpisodicMemory("agent1", "user1", "session1", adapter=adapter)
        event = {"action": "test_with_adapter", "result": "success"}
        
        # Log event
        memory.log_event(event)
        
        # Get episodes
        episodes = memory.get_episodes()
        assert len(episodes) == 1
        assert episodes[0]["action"] == "test_with_adapter"


class TestProceduralMemory:
    """Test ProceduralMemory functionality."""
    
    def test_memory_without_adapter(self):
        """Test procedural memory without adapter (in-memory fallback)."""
        memory = ProceduralMemory("agent1", "user1")
        procedure = {"steps": ["step1", "step2"], "description": "test procedure"}
        
        # Add procedure
        memory.add_procedure("test_proc", procedure)
        
        # Get procedure
        retrieved = memory.get_procedure("test_proc")
        assert retrieved is not None
        assert retrieved["steps"] == ["step1", "step2"]
        
        # List procedures
        procedures = memory.list_procedures()
        assert "test_proc" in procedures
    
    def test_memory_with_adapter(self):
        """Test procedural memory with adapter."""
        adapter = InMemoryAdapter()
        memory = ProceduralMemory("agent1", "user1", adapter=adapter)
        procedure = {"steps": ["step1", "step2"], "description": "test with adapter"}
        
        # Add procedure
        memory.add_procedure("test_proc_adapter", procedure)
        
        # Get procedure
        retrieved = memory.get_procedure("test_proc_adapter")
        assert retrieved is not None
        assert retrieved["description"] == "test with adapter"


class TestSemanticMemory:
    """Test SemanticMemory functionality."""
    
    def test_memory_without_adapter(self):
        """Test semantic memory without adapter (in-memory fallback)."""
        memory = SemanticMemory("agent1", "user1")
        fact = {"concept": "test_concept", "value": "test_value"}
        
        # Add fact
        memory.add_fact(fact)
        
        # Search facts
        results = memory.search("test_concept")
        assert len(results) >= 1
        assert any(f["concept"] == "test_concept" for f in results)
    
    def test_memory_with_adapter(self):
        """Test semantic memory with adapter."""
        adapter = InMemoryAdapter()
        memory = SemanticMemory("agent1", "user1", adapter=adapter)
        fact = {"concept": "test_with_adapter", "value": "adapter_value"}
        
        # Add fact
        memory.add_fact(fact)
        
        # Search facts
        results = memory.search("adapter")
        assert len(results) >= 0  # May be empty due to simple search implementation


class TestAgentMemory:
    """Test AgentMemory functionality."""
    
    def test_agent_memory_without_adapters(self):
        """Test agent memory without adapters (fallback to in-memory)."""
        agent_memory = AgentMemory(agent_id="agent1", user_id="user1", session_id="session1")
        
        # Check that all memory types are initialized
        assert agent_memory.short_term is not None
        assert agent_memory.episodic is not None
        assert agent_memory.procedural is not None
        assert agent_memory.semantic is not None
    
    def test_agent_memory_with_adapters(self):
        """Test agent memory with adapters."""
        adapters = {
            "short_term": InMemoryAdapter(),
            "episodic": InMemoryAdapter(),
            "procedural": InMemoryAdapter(),
            "semantic": InMemoryAdapter()
        }
        
        agent_memory = AgentMemory(
            agent_id="agent1", 
            user_id="user1", 
            session_id="session1",
            adapters=adapters
        )
        
        # Check that all memory types are initialized with adapters
        assert agent_memory.short_term.adapter is not None
        assert agent_memory.episodic.adapter is not None
        assert agent_memory.procedural.adapter is not None
        assert agent_memory.semantic.adapter is not None


class TestMemoryManager:
    """Test MemoryManager functionality."""
    
    def test_memory_manager_without_adapters(self):
        """Test memory manager without adapters."""
        manager = MemoryManager()
        
        # Get agent memory
        agent_memory = manager.get_agent_memory("agent1", "user1", "session1")
        assert agent_memory is not None
        assert agent_memory.agent_id == "agent1"
        assert agent_memory.user_id == "user1"
        assert agent_memory.session_id == "session1"
    
    def test_memory_manager_with_adapters(self):
        """Test memory manager with adapters."""
        adapters = {
            "short_term": InMemoryAdapter(),
            "episodic": InMemoryAdapter(),
            "procedural": InMemoryAdapter(),
            "semantic": InMemoryAdapter()
        }
        
        manager = MemoryManager(adapters=adapters)
        
        # Test short-term message operations
        message = Message(role="user", content="Test message")
        manager.add_short_term_message("agent1", "user1", "session1", message)
        
        messages = manager.get_short_term_messages("agent1", "user1", "session1")
        assert len(messages) == 1
        assert messages[0]["content"] == "Test message"
        
        # Test episodic operations
        event = {"action": "test", "result": "success"}
        manager.log_episode("agent1", "user1", "session1", event)
        
        # Test procedural operations
        procedure = {"steps": ["step1", "step2"]}
        manager.add_procedure("agent1", "user1", "test_proc", procedure)
        
        # Test semantic operations
        fact = {"concept": "test", "value": "fact"}
        manager.add_fact("agent1", "user1", fact)
        
        # Test search
        results = manager.search_semantic("agent1", "user1", "test")
        assert isinstance(results, list)
    
    def test_memory_manager_clear_operations(self):
        """Test memory manager clear operations."""
        adapters = {
            "short_term": InMemoryAdapter(),
            "episodic": InMemoryAdapter(),
            "procedural": InMemoryAdapter(),
            "semantic": InMemoryAdapter()
        }
        
        manager = MemoryManager(adapters=adapters)
        
        # Add some data
        message = Message(role="user", content="Test message")
        manager.add_short_term_message("agent1", "user1", "session1", message)
        
        # Clear specific memory types
        manager.clear_memory("agent1", "user1", "session1", memory_types=["short_term"])
        
        # Verify cleared
        messages = manager.get_short_term_messages("agent1", "user1", "session1")
        assert len(messages) == 0

from .react_planner import ReActPlanner
from .graph_planner import GraphPlanner
from .chain_planner import ChainPlanner

class PlannerFactory:
    @staticmethod
    def create_planner(planner_type: str, config: Dict[str, Any]):
        planners = {
            "react": ReActPlanner,
            "graph": <PERSON><PERSON><PERSON><PERSON>lan<PERSON>,
            "chain": ChainPlanner
        }
        return planners[planner_type](**config)
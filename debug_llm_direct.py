#!/usr/bin/env python3
"""
Direct test of LLM with search results to debug the issue.
"""

import os

# Set environment variables
os.environ["LLM_PROVIDER"] = "ollama"
os.environ["OLLAMA_MODEL"] = "gemma3:270m"
os.environ["OLLAMA_HOST"] = "http://127.0.0.1:11434"

def test_llm_direct():
    """Test LLM directly with search results."""
    
    print("🧪 Direct LLM Test with Search Results")
    print("=" * 50)
    
    try:
        from app.providers.factory import get_llm
        
        # Get LLM
        llm = get_llm()
        print(f"✅ LLM loaded: {type(llm).__name__}")
        
        # Create test search results
        test_sources = """[1] Samsung Galaxy S24 AI Features Review
URL: https://example.com/samsung-s24-ai
Content: The Samsung Galaxy S24 introduces advanced AI photography with improved night mode, AI-powered image enhancement, and real-time translation features. The device includes Samsung's new AI assistant capabilities and enhanced voice recognition.

[2] iPhone 15 AI Capabilities Analysis  
URL: https://example.com/iphone-15-ai
Content: iPhone 15 features Apple's A17 Pro chip with dedicated AI processing units. Key AI features include improved Siri functionality, AI-powered camera enhancements, and machine learning optimizations for battery life and performance.

[3] 2024 Smartphone AI Comparison
URL: https://example.com/ai-comparison-2024
Content: In 2024, both Samsung and Apple have significantly advanced their AI capabilities. Samsung focuses on photography and translation, while Apple emphasizes system-wide AI integration and privacy-focused machine learning."""
        
        # Test different prompt approaches
        test_cases = [
            {
                "name": "Direct Instruction",
                "system": "You are a research analyst. Write a detailed comparison report using the provided search results. Do not ask questions or say you're ready to help.",
                "user": f"Compare Samsung Galaxy S24 and iPhone 15 AI features.\n\nSearch Results:\n{test_sources}\n\nWrite a detailed comparison report using the search results above."
            },
            {
                "name": "Simple Command",
                "system": "Analyze the search results and write a comparison report.",
                "user": f"Query: Compare Samsung Galaxy S24 and iPhone 15 AI features\n\nSources:\n{test_sources}\n\nAnalyze and compare based on the sources above."
            },
            {
                "name": "No System Prompt",
                "system": "",
                "user": f"Using these search results, write a detailed comparison of Samsung Galaxy S24 vs iPhone 15 AI features:\n\n{test_sources}\n\nProvide a comprehensive analysis with specific details from the sources."
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 Test Case {i}: {test_case['name']}")
            print("-" * 40)
            
            try:
                if test_case['system']:
                    response = llm.chat(
                        messages=[{"role": "user", "content": test_case['user']}],
                        system_prompt=test_case['system'],
                        temperature=0.3
                    )
                else:
                    response = llm.chat(
                        messages=[{"role": "user", "content": test_case['user']}],
                        temperature=0.3
                    )
                
                # Handle different response formats
                if isinstance(response, dict):
                    content = response.get("content", "")
                    usage = response.get("usage", {})
                    print(f"   Input tokens: {usage.get('prompt_tokens', 0)}")
                    print(f"   Output tokens: {usage.get('completion_tokens', 0)}")
                else:
                    content = str(response)
                    print(f"   Response type: {type(response)}")
                
                print(f"   Response length: {len(content)} characters")
                
                # Check for generic responses
                generic_indicators = [
                    "I am ready to help",
                    "I have read the provided",
                    "Please provide me with",
                    "I am ready to proceed"
                ]
                
                has_generic = any(indicator.lower() in content.lower() for indicator in generic_indicators)
                
                # Check for actual content
                content_indicators = ["Samsung", "iPhone", "Galaxy", "S24", "A17", "AI"]
                content_score = sum(1 for indicator in content_indicators if indicator.lower() in content.lower())
                
                print(f"   Generic response: {'❌ YES' if has_generic else '✅ NO'}")
                print(f"   Content indicators: {content_score}/6")
                
                print(f"\n   Response preview:")
                print(f"   {content[:200]}...")
                
                if not has_generic and content_score >= 3:
                    print(f"   ✅ This approach works!")
                else:
                    print(f"   ❌ This approach has issues")
                    
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
        
        print(f"\n🎯 Summary:")
        print(f"   Test completed - check which approach works best")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_llm_direct()

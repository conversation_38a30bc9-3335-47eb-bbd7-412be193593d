from app.core.runner import <PERSON><PERSON><PERSON><PERSON>
from app.tools.registry import ToolRegistry
from app.tools.http_get import http_get
from app.tools.math_eval import math_eval
from app.memory.buffer_memory import InMemoryBuffer
from app.validation.guards import StepLimitValidator
from app.planners.mock_planner import <PERSON>ckPlanner
from app.core.types import Message

def test_smoke_run():
    registry = ToolRegistry()
    registry.register(http_get)
    registry.register(math_eval)
    runner = AgentRunner(
        planner=<PERSON><PERSON>Planner(),
        tools=registry,
        memory=InMemoryBuffer(),
        validators=[StepLimitValidator(max_steps=3)],
        policy={"allowed_tools": ["http.get", "math.eval"], "max_steps": 3},
    )
    res = runner.run("run1", "Fetch httpbin.org and compute 2+3", [Message(role="user", content="go")], {})
    assert "state" in res

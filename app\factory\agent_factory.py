"""
Agent Factory for creating and managing specialized agents.

This module provides the AgentFactory class for creating different types
of agents with configuration-driven setup and management.
"""

import json
import yaml
from pathlib import Path
from typing import Dict, Any, Union, Optional, Type, List
from loguru import logger

from ..core.exceptions import ConfigurationError, FactoryError
from ..agents.base import BaseAgent
from ..agents.customer_support import CustomerSupportAgent
from ..agents.sales import SalesAgent
from ..agents.research import ResearchAgent


def load_agent_config(config_path: Union[str, Path]) -> Dict[str, Any]:
    """
    Load agent configuration from a file.
    
    Supports YAML and JSON configuration files. The file format is
    determined by the file extension.
    
    Args:
        config_path: Path to the configuration file
        
    Returns:
        Dictionary containing the loaded configuration
        
    Raises:
        ConfigurationError: If file cannot be loaded or parsed
    """
    try:
        config_file = Path(config_path)
        
        if not config_file.exists():
            raise ConfigurationError(f"Configuration file not found: {config_path}")
        
        logger.debug(f"Loading configuration from: {config_path}")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            if config_file.suffix.lower() in ['.yaml', '.yml']:
                config = yaml.safe_load(f)
            elif config_file.suffix.lower() == '.json':
                config = json.load(f)
            else:
                raise ConfigurationError(
                    f"Unsupported configuration file format: {config_file.suffix}. "
                    "Supported formats: .yaml, .yml, .json"
                )
        
        if not isinstance(config, dict):
            raise ConfigurationError("Configuration must be a dictionary/object")
        
        # Validate required fields
        validated_config = validate_agent_config(config)
        
        logger.info(f"Successfully loaded configuration from {config_path}")
        return validated_config
        
    except (yaml.YAMLError, json.JSONDecodeError) as e:
        logger.error(f"Failed to parse configuration file {config_path}: {str(e)}")
        raise ConfigurationError(f"Configuration parsing failed: {str(e)}") from e
    except Exception as e:
        logger.error(f"Failed to load configuration from {config_path}: {str(e)}")
        raise ConfigurationError(f"Configuration loading failed: {str(e)}") from e


def validate_agent_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate agent configuration structure and values.
    
    Args:
        config: Configuration dictionary to validate
        
    Returns:
        Validated configuration dictionary
        
    Raises:
        ConfigurationError: If configuration is invalid
    """
    try:
        # Create a copy to avoid modifying the original
        validated_config = config.copy()
        
        # Set default values for optional fields
        validated_config.setdefault("tools", [])
        validated_config.setdefault("memory", {})
        validated_config.setdefault("planner", {})
        validated_config.setdefault("capabilities", [])
        
        # Validate tools configuration
        if not isinstance(validated_config["tools"], list):
            raise ConfigurationError("'tools' must be a list")
        
        # Validate memory configuration
        if not isinstance(validated_config["memory"], dict):
            raise ConfigurationError("'memory' must be a dictionary")
        
        # Validate planner configuration
        if not isinstance(validated_config["planner"], dict):
            raise ConfigurationError("'planner' must be a dictionary")
        
        # Validate capabilities
        if not isinstance(validated_config["capabilities"], list):
            raise ConfigurationError("'capabilities' must be a list")

        # Validate agent-specific parameters
        _validate_agent_specific_params(validated_config)

        logger.debug("Configuration validation completed successfully")
        return validated_config
        
    except Exception as e:
        logger.error(f"Configuration validation failed: {str(e)}")
        raise ConfigurationError(f"Invalid configuration: {str(e)}") from e


def _validate_agent_specific_params(config: Dict[str, Any]) -> None:
    """
    Validate agent-specific configuration parameters.

    Args:
        config: Configuration dictionary to validate

    Raises:
        ConfigurationError: If agent-specific parameters are invalid
    """
    # Validate escalation_threshold (for customer support agents)
    if "escalation_threshold" in config:
        threshold = config["escalation_threshold"]
        if not isinstance(threshold, (int, float)) or not (0 <= threshold <= 1):
            raise ConfigurationError(
                "escalation_threshold must be a number between 0 and 1"
            )

    # Validate max_discount (for sales agents)
    if "max_discount" in config:
        discount = config["max_discount"]
        if not isinstance(discount, (int, float)) or not (0 <= discount <= 1):
            raise ConfigurationError(
                "max_discount must be a number between 0 and 1"
            )

    # Validate max_sources (for research agents)
    if "max_sources" in config:
        sources = config["max_sources"]
        if not isinstance(sources, int) or sources <= 0:
            raise ConfigurationError(
                "max_sources must be a positive integer"
            )

    # Validate qualification_threshold (for sales agents)
    if "qualification_threshold" in config:
        threshold = config["qualification_threshold"]
        if not isinstance(threshold, (int, float)) or not (0 <= threshold <= 1):
            raise ConfigurationError(
                "qualification_threshold must be a number between 0 and 1"
            )

    # Validate fact_check_threshold (for research agents)
    if "fact_check_threshold" in config:
        threshold = config["fact_check_threshold"]
        if not isinstance(threshold, (int, float)) or not (0 <= threshold <= 1):
            raise ConfigurationError(
                "fact_check_threshold must be a number between 0 and 1"
            )


def save_agent_config(config: Dict[str, Any], config_path: Union[str, Path]) -> None:
    """
    Save agent configuration to a file.
    
    Args:
        config: Configuration dictionary to save
        config_path: Path where to save the configuration
        
    Raises:
        ConfigurationError: If saving fails
    """
    try:
        config_file = Path(config_path)
        
        # Create directory if it doesn't exist
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        logger.debug(f"Saving configuration to: {config_path}")
        
        with open(config_file, 'w', encoding='utf-8') as f:
            if config_file.suffix.lower() in ['.yaml', '.yml']:
                yaml.safe_dump(config, f, default_flow_style=False, indent=2)
            elif config_file.suffix.lower() == '.json':
                json.dump(config, f, indent=2, ensure_ascii=False)
            else:
                raise ConfigurationError(
                    f"Unsupported configuration file format: {config_file.suffix}"
                )
        
        logger.info(f"Configuration saved successfully to {config_path}")
        
    except Exception as e:
        logger.error(f"Failed to save configuration to {config_path}: {str(e)}")
        raise ConfigurationError(f"Configuration saving failed: {str(e)}") from e


def load_system_config(config_path: Union[str, Path]) -> Dict[str, Any]:
    """
    Load system-wide configuration.
    
    Args:
        config_path: Path to the system configuration file
        
    Returns:
        System configuration dictionary
        
    Raises:
        ConfigurationError: If loading fails
    """
    try:
        config = load_agent_config(config_path)
        
        # Validate system-specific configuration
        required_sections = ["logging", "database", "api"]
        for section in required_sections:
            if section not in config:
                logger.warning(f"Missing system configuration section: {section}")
        
        return config
        
    except Exception as e:
        logger.error(f"Failed to load system configuration: {str(e)}")
        raise ConfigurationError(f"System configuration loading failed: {str(e)}") from e


class AgentFactory:
    """
    Factory class for creating and managing specialized agents.

    This factory provides a centralized way to create different types of agents
    with configuration-driven setup and management capabilities.
    """

    # Registry of available agent types
    _agent_registry: Dict[str, Dict[str, Any]] = {
        "customer_support": {"class": CustomerSupportAgent},
        "sales": {"class": SalesAgent},
        "research": {"class": ResearchAgent}
    }

    @classmethod
    def get_available_agent_types(cls) -> List[str]:
        """
        Get list of available agent types.

        Returns:
            List of available agent type names
        """
        return list(cls._agent_registry.keys())

    @classmethod
    def get_default_config(cls, agent_type: str) -> Dict[str, Any]:
        """
        Get default configuration for an agent type.

        Args:
            agent_type: Type of agent

        Returns:
            Default configuration dictionary

        Raises:
            FactoryError: If agent type is unknown
        """
        if agent_type not in cls._agent_registry:
            available_types = ", ".join(cls.get_available_agent_types())
            raise FactoryError(
                f"Unknown agent type: {agent_type}. "
                f"Available types: {available_types}"
            )

        return cls._agent_registry[agent_type]["default_config"].copy()

    @classmethod
    def create_agent(
        cls,
        agent_type: str,
        config: Optional[Dict[str, Any]] = None,
        name: Optional[str] = None
    ) -> BaseAgent:
        """
        Create an agent of the specified type.

        Args:
            agent_type: Type of agent to create (e.g., 'sales', 'customer_support', 'research')
            config: Optional inline configuration to completely override file config
            name: Optional custom name for the agent

        Returns:
            Created agent instance

        Raises:
            FactoryError: If agent creation fails
        """
        try:
            logger.info(f"Creating agent of type: {agent_type}")

            if agent_type not in cls._agent_registry:
                available_types = ", ".join(cls.get_available_agent_types())
                raise FactoryError(
                    f"Unknown agent type: {agent_type}. "
                    f"Available types: {available_types}"
                )

            agent_class = cls._agent_registry[agent_type]["class"]

            # Determine config file path
            config_dir = Path(__file__).parent.parent / "config" / "agents"
            yaml_path = config_dir / f"{agent_type}.yaml"
            json_path = config_dir / f"{agent_type}.json"

            # Load configuration from file unless overridden
            if config is not None:
                final_config = config.copy()
                logger.debug("Using API-provided config, overriding file config completely")
            else:
                if yaml_path.exists():
                    file_config = load_agent_config(yaml_path)
                elif json_path.exists():
                    file_config = load_agent_config(json_path)
                else:
                    raise FactoryError(f"No config file found for agent type '{agent_type}' in {config_dir}")
                final_config = file_config.copy()
                logger.debug(f"Loaded configuration from file: {yaml_path if yaml_path.exists() else json_path}")

            # Add custom name if provided
            if name:
                final_config["custom_name"] = name

            # Validate configuration
            validated_config = validate_agent_config(final_config)

            # Create agent instance
            agent = agent_class(validated_config)

            logger.info(f"Successfully created {agent_type} agent")
            return agent

        except (FactoryError, ConfigurationError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error creating agent: {str(e)}")
            raise FactoryError(f"Agent creation failed: {str(e)}") from e


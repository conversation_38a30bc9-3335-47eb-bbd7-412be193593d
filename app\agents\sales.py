"""
Sales Agent implementation.

This module provides a specialized agent for sales operations,
lead qualification, product recommendations, and deal management.
"""

from typing import Dict, Any, List, Optional
from loguru import logger
from datetime import datetime

from .base import BaseAgent
from ..core.exceptions import AgentError


class SalesAgent(BaseAgent):
    """
    Specialized agent for sales operations.
    
    This agent handles lead qualification, product recommendations,
    pricing negotiations, deal management, and sales pipeline tracking.
    
    Capabilities:
        - Lead qualification
        - Product recommendation
        - Pricing negotiation
        - Deal management
        - Pipeline tracking
        - Customer profiling
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Sales Agent.

        Args:
            config: Configuration dictionary containing agent parameters
        """
        # Use custom name if provided, otherwise use default
        agent_name = config.get("custom_name", "sales")
        super().__init__(agent_name, config)
        self._setup_sales_capabilities()
        logger.info("Sales Agent initialized successfully")
    
    def _setup_sales_capabilities(self) -> None:
        """Setup sales specific capabilities and configuration."""
        try:
            # Add sales-specific capabilities
            capabilities = [
                "lead_qualification",
                "product_recommendation",
                "pricing_negotiation",
                "deal_management",
                "pipeline_tracking",
                "customer_profiling"
            ]
            
            for capability in capabilities:
                self.add_capability(capability)
            
            # Setup sales-specific configuration
            self.qualification_threshold = self.config.get("qualification_threshold", 0.7)
            self.max_discount = self.config.get("max_discount", 0.15)
            self.product_catalog = self.config.get("product_catalog", [])
            self.sales_stages = self.config.get("sales_stages", ["lead", "qualified", "proposal", "negotiation", "closed"])

            # Setup agent persona and system prompt
            self.persona = self.config.get("persona", self._get_default_persona())
            self.system_prompt = self.config.get("system_prompt", self._get_default_system_prompt())

            # Setup LLM provider configuration
            self.llm_provider = self.config.get("llm_provider", "ollama")
            self.llm_model = self.config.get("llm_model", "gemma3:270m")

            logger.debug(f"Setup {len(capabilities)} sales capabilities")
            
        except Exception as e:
            logger.error(f"Failed to setup sales capabilities: {str(e)}")
            raise AgentError(f"Sales setup failed: {str(e)}") from e

    def _get_default_persona(self) -> str:
        """Get the default persona for sales agent."""
        return """You are Jordan, a dynamic and results-driven sales professional with 7+ years of experience
        in consultative selling. You are charismatic, persuasive, and genuinely care about helping customers
        find the right solutions. You excel at building rapport, understanding customer needs, and presenting
        value propositions that resonate. You're competitive but ethical, always putting customer success first."""

    def _get_default_system_prompt(self) -> str:
        """Get the default system prompt for sales agent."""
        return """You are a professional sales agent. Your role is to:

1. **Qualify leads** - Understand customer needs, budget, timeline, and decision-making process
2. **Build relationships** - Establish trust and rapport with prospects
3. **Present solutions** - Match customer needs with appropriate products/services
4. **Handle objections** - Address concerns professionally and provide reassurance
5. **Close deals** - Guide customers through the purchasing process

**Sales Process:**
- Discovery: Ask open-ended questions to understand pain points
- Presentation: Demonstrate value and ROI of your solutions
- Objection Handling: Listen, acknowledge, and provide solutions
- Closing: Ask for the sale and guide next steps

**Guidelines:**
- Always lead with value, not features
- Use consultative selling approach
- Provide social proof and case studies when relevant
- Be transparent about pricing and terms
- Follow up consistently and professionally
- Maintain accurate records of all interactions

**Tone:** Professional, confident, helpful, and customer-focused
**Goal:** Build long-term relationships while achieving sales targets"""

    def process(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process sales query and provide sales assistance.
        
        Args:
            query: Customer inquiry or sales request
            context: Additional context including customer info, deal history
            
        Returns:
            Dictionary containing sales response and recommendations
            
        Raises:
            AgentError: If processing fails
        """
        try:
            logger.info(f"Processing sales query: {query[:100]}...")
            
            # Extract sales context
            customer_id = context.get("customer_id")
            deal_id = context.get("deal_id")
            budget = context.get("budget")
            
            # Analyze sales intent
            intent = self._analyze_sales_intent(query, context)
            
            # Qualify lead if new customer
            qualification = self._qualify_lead(query, context)
            
            # Generate sales response
            response_data = self._generate_sales_response(query, intent, qualification, context)
            
            # Recommend products if applicable
            recommendations = self._recommend_products(query, context, qualification)
            
            # Calculate pricing if requested
            pricing = self._calculate_pricing(recommendations, context)
            
            result = {
                "response": response_data["message"],
                "intent": intent,
                "lead_qualification": qualification,
                "product_recommendations": recommendations,
                "pricing": pricing,
                "next_steps": response_data["next_steps"],
                "confidence": response_data["confidence"],
                "metadata": {
                    "agent_id": self.agent_id,
                    "processing_time": datetime.utcnow().isoformat(),
                    "sales_stage": self._determine_sales_stage(intent, qualification),
                    "tools_used": response_data.get("tools_used", [])
                }
            }
            
            logger.info(f"Sales query processed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Failed to process sales query: {str(e)}")
            raise AgentError(f"Sales processing failed: {str(e)}") from e
    
    def _analyze_sales_intent(self, query: str, context: Dict[str, Any]) -> str:
        """Analyze sales intent from query."""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ["price", "cost", "pricing", "quote"]):
            return "pricing_inquiry"
        elif any(word in query_lower for word in ["demo", "trial", "test", "try"]):
            return "demo_request"
        elif any(word in query_lower for word in ["features", "capabilities", "what can", "how does"]):
            return "product_inquiry"
        elif any(word in query_lower for word in ["buy", "purchase", "order", "get started"]):
            return "purchase_intent"
        elif any(word in query_lower for word in ["compare", "vs", "alternative", "competitor"]):
            return "comparison_request"
        else:
            return "general_sales_inquiry"
    
    def _qualify_lead(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Qualify lead based on query and context."""
        score = 0.5  # Base score
        
        # Budget qualification
        budget = context.get("budget")
        if budget:
            if budget > 10000:
                score += 0.3
            elif budget > 1000:
                score += 0.2
            else:
                score += 0.1
        
        # Authority qualification
        if context.get("decision_maker"):
            score += 0.2
        
        # Need qualification
        query_lower = query.lower()
        if any(word in query_lower for word in ["need", "require", "must have", "looking for"]):
            score += 0.2
        
        # Timeline qualification
        if any(word in query_lower for word in ["urgent", "asap", "immediately", "soon"]):
            score += 0.1
        
        qualification_level = "high" if score >= self.qualification_threshold else "medium" if score >= 0.5 else "low"
        
        return {
            "score": min(1.0, score),
            "level": qualification_level,
            "factors": {
                "budget": budget is not None,
                "authority": context.get("decision_maker", False),
                "need": "need" in query_lower,
                "timeline": any(word in query_lower for word in ["urgent", "soon"])
            }
        }
    
    def _generate_sales_response(self, query: str, intent: str, qualification: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate appropriate sales response."""
        response_templates = {
            "pricing_inquiry": "I'd be happy to provide pricing information. Let me understand your specific needs better.",
            "demo_request": "Great! I can arrange a personalized demo for you. When would be a good time?",
            "product_inquiry": "Let me tell you about our product features that would be most relevant to your needs.",
            "purchase_intent": "Excellent! I can help you get started right away. Let's discuss the best package for you.",
            "comparison_request": "I can help you understand how we compare to alternatives and what makes us unique.",
            "general_sales_inquiry": "Thank you for your interest! I'm here to help you find the perfect solution."
        }
        
        base_message = response_templates.get(intent, response_templates["general_sales_inquiry"])
        
        # Customize based on qualification
        if qualification["level"] == "high":
            base_message += " Based on your requirements, I believe we have an excellent fit."
        
        next_steps = self._get_next_steps(intent, qualification)
        
        return {
            "message": base_message,
            "confidence": 0.9,
            "next_steps": next_steps,
            "tools_used": ["sales_qualifier", "product_matcher"]
        }
    
    def _recommend_products(self, query: str, context: Dict[str, Any], qualification: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Recommend products based on query and qualification."""
        # Mock product recommendations
        recommendations = [
            {
                "product_id": "prod_001",
                "name": "Professional Plan",
                "description": "Perfect for growing businesses",
                "price": 99.99,
                "match_score": 0.9,
                "features": ["Advanced analytics", "Priority support", "Custom integrations"]
            },
            {
                "product_id": "prod_002", 
                "name": "Enterprise Plan",
                "description": "For large organizations",
                "price": 299.99,
                "match_score": 0.7,
                "features": ["Unlimited users", "Advanced security", "Dedicated support"]
            }
        ]
        
        # Filter based on qualification
        if qualification["level"] == "low":
            recommendations = [r for r in recommendations if r["price"] < 100]
        
        return recommendations[:3]  # Return top 3
    
    def _calculate_pricing(self, recommendations: List[Dict[str, Any]], context: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate pricing with potential discounts."""
        if not recommendations:
            return {"total": 0, "discount": 0, "final_price": 0}
        
        base_price = recommendations[0]["price"]
        discount = 0
        
        # Apply discounts based on context
        if context.get("volume_discount"):
            discount = min(0.1, self.max_discount)
        
        if context.get("annual_payment"):
            discount = min(discount + 0.05, self.max_discount)
        
        final_price = base_price * (1 - discount)
        
        return {
            "base_price": base_price,
            "discount_percentage": discount,
            "discount_amount": base_price * discount,
            "final_price": final_price,
            "currency": "USD"
        }
    
    def _determine_sales_stage(self, intent: str, qualification: Dict[str, Any]) -> str:
        """Determine current sales stage."""
        if intent == "purchase_intent" and qualification["level"] == "high":
            return "negotiation"
        elif intent in ["demo_request", "pricing_inquiry"] and qualification["level"] in ["high", "medium"]:
            return "proposal"
        elif qualification["level"] in ["medium", "high"]:
            return "qualified"
        else:
            return "lead"
    
    def _get_next_steps(self, intent: str, qualification: Dict[str, Any]) -> List[str]:
        """Get recommended next steps."""
        steps_map = {
            "pricing_inquiry": ["Send detailed pricing", "Schedule pricing call", "Prepare proposal"],
            "demo_request": ["Schedule demo", "Prepare demo environment", "Send calendar invite"],
            "product_inquiry": ["Send product information", "Schedule consultation", "Provide case studies"],
            "purchase_intent": ["Prepare contract", "Schedule closing call", "Process order"],
            "comparison_request": ["Send comparison sheet", "Schedule competitive analysis", "Provide references"]
        }
        return steps_map.get(intent, ["Follow up", "Gather requirements", "Schedule next call"])


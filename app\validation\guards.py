from __future__ import annotations
from typing import Dict, Any, List
from .base import Validator

class StepLimitValidator:
    def __init__(self, max_steps: int = 5): self.max_steps = max_steps
    def check(self, context: Dict[str, Any]) -> List[str]:
        step = context.get("step", 0)
        return ["step_limit_exceeded"] if step > self.max_steps else []

class BudgetValidator:
    def __init__(self, max_api_calls: int | None = None):
        self.max_api_calls = max_api_calls
    def check(self, context: Dict[str, Any]) -> List[str]:
        # placeholder: wire to counters if you track calls
        return []

class TokenBudgetValidator:
    def __init__(self, max_tokens: int = 10000):
        self.max_tokens = max_tokens
    
    def check(self, context: Dict[str, Any]) -> List[str]:
        tokens_used = context.get("tokens_used", 0)
        return ["token_budget_exceeded"] if tokens_used > self.max_tokens else []

class ContentValidator:
    def __init__(self, forbidden_words: List[str] = None):
        self.forbidden_words = forbidden_words or []
    
    def check(self, context: Dict[str, Any]) -> List[str]:
        violations = []
        history = context.get("history", [])
        for msg in history:
            if any(word in msg.content.lower() for word in self.forbidden_words):
                violations.append("forbidden_content_detected")
        return violations

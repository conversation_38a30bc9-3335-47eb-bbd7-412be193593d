from __future__ import annotations
from typing import List, Dict, Any
from pydantic import BaseModel, ValidationError
from ..core.types import Plan, Action, Message
from ..providers.base import LLM

class ReActPlanner:
    def __init__(self, llm: LLM, allowed_tools: list[str]):
        self.llm = llm
        self.allowed_tools = allowed_tools

    def plan(self, goal: str, history: List[Message], state: Dict[str, Any]) -> Plan:
        prompt = (
            "You are a planner. Given a goal, produce a JSON plan with actions.\n"
            f"Allowed tools: {self.allowed_tools}\n"
            "Schema: {\"rationale\": str, \"actions\": [{\"tool\": str, \"params\": {}, \"reason\": str}]}\n"
            f"Goal: {goal}\n"
            "Return ONLY JSON."
        )
        content = self.llm.generate(prompt)
        try:
            data = self.llm.parse_json(content)
            acts = [Action(tool=a["tool"], params=a.get("params", {}), reason=a.get("reason","")) for a in data.get("actions", [])]
            return Plan(actions=acts, rationale=data.get("rationale", ""))
        except (ValidationError, Exception):
            # fallback: no-op
            return Plan(actions=[], rationale="Failed to parse plan")

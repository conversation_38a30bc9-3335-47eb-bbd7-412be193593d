"""
Agent API routes for the Agentic AI Framework.

This module provides REST API endpoints for agent management,
including creation, interaction, and multi-agent orchestration.
"""

from __future__ import annotations
import os
import time
import uuid
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field
from loguru import logger

try:
    from dotenv import load_dotenv  # type: ignore
except Exception:
    load_dotenv = None  # optional dependency

from ...core.types import Message
from ...core.runner import AgentRunner
from ...core.types import Message
from ...core.exceptions import AgentError, FactoryError, ConfigurationError
from ...providers.factory import get_llm, ProviderLoadError
from ...tools.registry import ToolRegistry
from ...tools.http_get import http_get
from ...tools.math_eval import math_eval
from ...tools.searxng_search import searxng_search
from ...validation.guards import StepLimitValidator, TokenBudgetValidator, ContentValidator
from ...policies.loader import load_policy
from ...planners.mock_planner import MockPlanner
from ...planners.react_planner import ReActPlanner
from ...providers.factory import get_llm, ProviderLoadError
from ..schemas import RunRequest, MultiModalMessage
from ...factory.agent_factory import AgentFactory
from ...orchestrators.multi_agent import MultiAgentOrchestrator


# API Models
class CreateAgentRequest(BaseModel):
    """Request model for creating an agent."""
    agent_type: str = Field(..., description="Type of agent to create")
    config: Optional[Dict[str, Any]] = Field(None, description="Inline configuration")
    name: Optional[str] = Field(None, description="Custom agent name")


class CreateAgentResponse(BaseModel):
    """Response model for agent creation."""
    agent_id: str = Field(..., description="Unique identifier for the created agent")
    agent_type: str = Field(..., description="Type of the created agent")
    status: str = Field(..., description="Creation status")
    capabilities: List[str] = Field(..., description="Agent capabilities")
    created_at: str = Field(..., description="Creation timestamp")


class ChatRequest(BaseModel):
    """Request model for chatting with an agent."""
    message: str = Field(..., description="Message to send to the agent")
    session_id: Optional[str] = Field(None, description="Session ID for conversation continuity")
    context: Dict[str, Any] = Field(default_factory=dict, description="Additional context")


class ChatResponse(BaseModel):
    """Response model for agent chat."""
    response: str = Field(..., description="Agent's response")
    agent_id: str = Field(..., description="ID of the responding agent")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Response metadata")


class MultiAgentRequest(BaseModel):
    """Request model for multi-agent collaboration."""
    query: str = Field(..., description="Query for multi-agent processing")
    agents: List[str] = Field(..., description="List of agent IDs to involve")
    collaboration_mode: str = Field(default="sequential", description="Collaboration mode")


class UpdateAgentConfigRequest(BaseModel):
    """Request model for updating agent configuration."""
    config: Dict[str, Any] = Field(..., description="New configuration parameters")
    merge: bool = Field(True, description="Whether to merge with existing config or replace")


class BulkCreateAgentRequest(BaseModel):
    """Request model for bulk agent creation."""
    agents: List[CreateAgentRequest] = Field(..., description="List of agents to create")


class AgentHealthResponse(BaseModel):
    """Response model for agent health check."""
    agent_id: str = Field(..., description="Agent identifier")
    status: str = Field(..., description="Health status")
    name: str = Field(..., description="Agent name")
    uptime: float = Field(..., description="Agent uptime in seconds")
    capabilities_count: int = Field(..., description="Number of capabilities")
    error: Optional[str] = Field(None, description="Error message if unhealthy")


class AgentConfigResponse(BaseModel):
    """Response model for agent configuration."""
    agent_id: str = Field(..., description="Agent identifier")
    config: Dict[str, Any] = Field(..., description="Current configuration")
    last_updated: str = Field(..., description="Last update timestamp")


# Router setup
router = APIRouter(prefix="/agents")

# Load environment variables
try:
    project_root = Path(__file__).resolve().parents[3]
    env_path = project_root / ".env"
    if load_dotenv is not None:
        load_dotenv(dotenv_path=str(env_path) if env_path.exists() else None, override=False)
except Exception as e:
    logger.warning(f"Failed to load .env file: {str(e)}")

# Global components setup
registry = ToolRegistry()
registry.register(http_get)
registry.register(math_eval)
registry.register(searxng_search)


 # Policy loading with error handling
try:
    # Use a generic default policy, optionally allow override via env/config
    policy_path = os.getenv("AGENT_POLICY_PATH")
    if policy_path and os.path.exists(policy_path):
        policy = load_policy(policy_path)
    else:
        policy = {"max_steps": 5, "budget_limit": 1000, "allowed_tools": ["http.get", "math.eval"]}
except Exception as e:
    logger.warning(f"Failed to load policy: {str(e)}, using default policy")
    policy = {"max_steps": 5, "budget_limit": 1000, "allowed_tools": ["http.get", "math.eval"]}

validators = [
    StepLimitValidator(max_steps=int(policy.get("max_steps", 5))),
    TokenBudgetValidator(max_tokens=policy.get("budget_limit", 1000)),
    ContentValidator(forbidden_words=["sensitive", "private"])
]

# Planner setup with fallback
try:
    if os.getenv("LLM_PROVIDER"):
        llm = get_llm()
        planner = ReActPlanner(llm=llm, allowed_tools=[t.spec.name for t in registry.list()])
        logger.info(f"Using ReActPlanner with provider {os.getenv('LLM_PROVIDER')}")
    else:
        planner = MockPlanner()
        logger.info("LLM_PROVIDER not set - using MockPlanner")
except ProviderLoadError as e:
    logger.warning(f"Provider error: {e} — falling back to MockPlanner")
    planner = MockPlanner()


# AgentRunner should be initialized per agent, using the agent's linked memory adapters
# Remove global 'memory' and use agent.memory when creating runner
# Example (inside an endpoint):
# runner = AgentRunner(planner=planner, tools=registry, memory=agent.memory, validators=validators, policy=policy)

# In-memory agent storage (would be database in production)
_active_agents: Dict[str, Any] = {}


@router.post("/run", response_model=Dict[str, Any])
async def run_agent(req: RunRequest):
    """
    Run a single agent with the provided goal.
    
    Args:
        req: Request containing the goal and optional parameters
        
    Returns:
        Agent execution result
        
    Raises:
        HTTPException: If agent execution fails
    """
    try:
        run_id = str(uuid.uuid4())
        history = [Message(role="user", content=req.goal)]
        
        logger.info(f"Running agent with goal: {req.goal[:100]}...")
        agent_id = req.agent_id if hasattr(req, 'agent_id') else None
        agent = _active_agents.get(agent_id, {}).get("agent") if agent_id else None
        runner = AgentRunner(planner=planner, tools=registry, memory=agent.memory if agent else {}, validators=validators, policy=policy)
        result = runner.run(run_id=run_id, goal=req.goal, history=history, state={})
        
        logger.info(f"Agent run completed successfully: {run_id}")
        return result
   
    except Exception as e:
        logger.error(f"Agent run failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Agent run failed: {str(e)}"
        )

        



@router.post("/create", response_model=CreateAgentResponse)
async def create_agent(req: CreateAgentRequest):
    """
    Create a new specialized agent.
    
    Args:
        req: Request containing agent type and configuration
        
    Returns:
        Information about the created agent
        
    Raises:
        HTTPException: If agent creation fails
    """
    try:
        logger.info(f"Creating agent of type: {req.agent_type}")
        
        # Create agent using factory
        agent = AgentFactory.create_agent(
            agent_type=req.agent_type,
            config=req.config,
            name=req.name
        )
        
        # Generate unique agent ID
        agent_id = req.name or f"{req.agent_type}_{uuid.uuid4().hex[:8]}"
        
        # Store agent in memory (would be database in production)
        created_at = datetime.now(timezone.utc).isoformat()
        _active_agents[agent_id] = {
            "agent": agent,
            "created_at": created_at,
            "type": req.agent_type
        }

        response = CreateAgentResponse(
            agent_id=agent_id,
            agent_type=req.agent_type,
            status="created",
            capabilities=agent.capabilities,
            created_at=created_at
        )
        
        logger.info(f"Agent created successfully: {agent_id}")
        return response
        
    except (FactoryError, ConfigurationError) as e:
        logger.error(f"Agent creation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error during agent creation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Agent creation failed: {str(e)}"
        )


@router.get("/types", response_model=List[str])
async def get_agent_types():
    """
    Get list of available agent types.
    
    Returns:
        List of available agent type names
    """
    try:
        types = AgentFactory.get_available_agent_types()
        logger.debug(f"Retrieved {len(types)} agent types")
        return types
    except Exception as e:
        logger.error(f"Failed to get agent types: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve agent types"
        )


@router.get("/{agent_id}/info", response_model=Dict[str, Any])
async def get_agent_info(agent_id: str):
    """
    Get information about a specific agent.
    
    Args:
        agent_id: ID of the agent to query
        
    Returns:
        Agent information and status
        
    Raises:
        HTTPException: If agent not found
    """
    try:
        if agent_id not in _active_agents:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent not found: {agent_id}"
            )
        
        agent_data = _active_agents[agent_id]
        agent = agent_data["agent"]
        
        info = agent.get_info()
        info.update({
            "agent_id": agent_id,
            "created_at": agent_data["created_at"],
            "type": agent_data["type"]
        })
        
        return info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get agent info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve agent information"
        )


@router.post("/{agent_id}/chat", response_model=ChatResponse)
async def chat_with_agent(agent_id: str, req: ChatRequest):
    """
    Chat with a specific agent using LLM provider and session memory.

    Args:
        agent_id: ID of the agent to chat with
        req: Chat request containing message, session_id, and context

    Returns:
        Agent's response with conversation context

    Raises:
        HTTPException: If agent not found or chat fails
    """
    try:
        if agent_id not in _active_agents:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent not found: {agent_id}"
            )

        agent = _active_agents[agent_id]["agent"]

        # Generate session ID if not provided (use agent_id for consistency)
        session_id = req.session_id.strip() if req.session_id and req.session_id.strip() else f"{agent_id}_session"

        logger.info(f"Processing chat message for agent {agent_id}, session {session_id}")

        # Use agent's memory manager for storing and retrieving messages
        memory_manager = getattr(agent, "memory_manager", None)
        user_id = req.context.get("user_id", "user")

        if memory_manager:
            # Short term memory for conversation
            msg = Message(role="user", content=req.message)
            memory_manager.add_short_term_message(agent_id, user_id, session_id, msg)
            conversation_history = memory_manager.get_short_term_messages(
                agent_id, user_id, session_id, limit=20
            )

            # Episodic memory: log event
            memory_manager.log_episode(agent_id, user_id, session_id, {
                "user_id": user_id,
                "session_id": session_id,
                "message": req.message,
                "timestamp": time.time(),
                "role": "user"
            })
        else:
            # Fallback for agents without memory manager
            conversation_history = []
            logger.warning(f"Agent {agent_id} has no memory manager - using empty conversation history")

        # Prepare enhanced context with conversation history
        enhanced_context = {
            **req.context,
            "session_id": session_id,
            "conversation_history": conversation_history,
            "message_count": len(conversation_history),
            "is_follow_up": len(conversation_history) > 0
        }

        logger.info(f"Executing agent processing pipeline for {agent_id}")

        # Use the agent's full processing pipeline (includes planning, tool execution, LLM integration)
        start_time = time.time()
        result = agent.process(req.message, enhanced_context)
        processing_time = time.time() - start_time

        # Extract response from agent result
        assistant_response = result.get("response", "No response generated")

        # Add assistant response to short term memory
        if memory_manager:
            assistant_msg = Message(role="assistant", content=assistant_response)
            memory_manager.add_short_term_message(agent_id, user_id, session_id, assistant_msg)

            # Optionally log to episodic memory
            memory_manager.log_episode(agent_id, user_id, session_id, {
                "user_id": user_id,
                "session_id": session_id,
                "message": assistant_response,
                "timestamp": time.time(),
                "role": "assistant"
            })

        # Prepare generic response with agent metadata
        response = ChatResponse(
            response=assistant_response,
            agent_id=agent_id,
            metadata={
                "session_id": session_id,
                "conversation_length": len(conversation_history) + 1,
                "processing_time": processing_time,
                "agent_type": type(agent).__name__,
                "llm_provider": getattr(agent, 'llm_provider', 'unknown'),
                "llm_model": getattr(agent, 'llm_model', 'unknown'),
                "persona": getattr(agent, 'persona', '')[:100] + "..." if hasattr(agent, 'persona') else '',
                **result.get("metadata", {})
            }
        )

        logger.info(f"Chat processed successfully for agent {agent_id}, session {session_id}")
        return response

    except HTTPException:
        raise
    except AgentError as e:
        logger.error(f"Agent processing error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Chat processing failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Chat processing failed"
        )


@router.delete("/{agent_id}")
async def delete_agent(agent_id: str):
    """
    Delete an agent.
    
    Args:
        agent_id: ID of the agent to delete
        
    Returns:
        Deletion confirmation
        
    Raises:
        HTTPException: If agent not found
    """
    try:
        if agent_id not in _active_agents:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent not found: {agent_id}"
            )
        
        del _active_agents[agent_id]
        logger.info(f"Agent deleted successfully: {agent_id}")
        
        return {"message": f"Agent {agent_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete agent: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Agent deletion failed"
        )


@router.get("/", response_model=List[Dict[str, Any]])
async def list_agents():
    """
    List all active agents.
    
    Returns:
        List of active agents with their information
    """
    try:
        agents_info = []
        for agent_id, agent_data in _active_agents.items():
            agent = agent_data["agent"]
            info = agent.get_info()
            info.update({
                "agent_id": agent_id,
                "created_at": agent_data["created_at"],
                "type": agent_data["type"]
            })
            agents_info.append(info)
        
        logger.debug(f"Listed {len(agents_info)} active agents")
        return agents_info
        
    except Exception as e:
        logger.error(f"Failed to list agents: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list agents"
        )


@router.post("/multi-agent/collaborate", response_model=Dict[str, Any])
async def multi_agent_collaborate(req: MultiAgentRequest):
    """
    Execute multi-agent collaboration.
    
    Args:
        req: Multi-agent collaboration request
        
    Returns:
        Collaboration result
        
    Raises:
        HTTPException: If collaboration fails
    """
    try:
        logger.info(f"Starting multi-agent collaboration with {len(req.agents)} agents")
        
        # Validate all agents exist
        agents = []
        for agent_id in req.agents:
            if agent_id not in _active_agents:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Agent not found: {agent_id}"
                )
            agents.append(_active_agents[agent_id]["agent"])
        
        # Create orchestrator
        orchestrator = MultiAgentOrchestrator(agents)
        
        # Process collaboration (placeholder implementation)
        result = await orchestrator.process_collaborative(req.query)
        
        logger.info("Multi-agent collaboration completed successfully")
        return result or {"message": "Collaboration completed", "agents": req.agents}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Multi-agent collaboration failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Multi-agent collaboration failed"
        )


@router.get("/{agent_id}/health", response_model=AgentHealthResponse)
async def get_agent_health(agent_id: str):
    """
    Get health status of a specific agent.

    Args:
        agent_id: ID of the agent to check

    Returns:
        Agent health information

    Raises:
        HTTPException: If agent not found or health check fails
    """
    try:
        if agent_id not in _active_agents:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent {agent_id} not found"
            )

        agent_data = _active_agents[agent_id]
        agent = agent_data["agent"]

        # Perform health check
        health = agent.health_check()

        response = AgentHealthResponse(
            agent_id=agent_id,
            status=health["status"],
            name=health["name"],
            uptime=health.get("uptime", 0.0),
            capabilities_count=health.get("capabilities_count", len(agent.capabilities)),
            error=health.get("error")
        )

        logger.info(f"Health check completed for agent: {agent_id}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Health check failed for agent {agent_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Health check failed"
        )


@router.get("/{agent_id}/config", response_model=AgentConfigResponse)
async def get_agent_config(agent_id: str):
    """
    Get configuration of a specific agent.

    Args:
        agent_id: ID of the agent

    Returns:
        Agent configuration

    Raises:
        HTTPException: If agent not found
    """
    try:
        if agent_id not in _active_agents:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent {agent_id} not found"
            )

        agent_data = _active_agents[agent_id]
        agent = agent_data["agent"]

        response = AgentConfigResponse(
            agent_id=agent_id,
            config=agent.config,
            last_updated=agent_data.get("last_updated", agent_data["created_at"])
        )

        logger.info(f"Configuration retrieved for agent: {agent_id}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get config for agent {agent_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve agent configuration"
        )


@router.put("/{agent_id}/config", response_model=AgentConfigResponse)
async def update_agent_config(agent_id: str, req: UpdateAgentConfigRequest):
    """
    Update configuration of a specific agent.

    Args:
        agent_id: ID of the agent
        req: Configuration update request

    Returns:
        Updated agent configuration

    Raises:
        HTTPException: If agent not found or update fails
    """
    try:
        if agent_id not in _active_agents:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent {agent_id} not found"
            )

        agent_data = _active_agents[agent_id]
        agent = agent_data["agent"]

        # Update configuration
        if req.merge:
            # Merge with existing configuration
            agent.config.update(req.config)
        else:
            # Replace configuration
            agent.config = req.config.copy()

        # Update timestamp
        updated_at = datetime.now(timezone.utc).isoformat()
        _active_agents[agent_id]["last_updated"] = updated_at

        # Re-setup agent with new configuration
        agent._setup_agent()

        response = AgentConfigResponse(
            agent_id=agent_id,
            config=agent.config,
            last_updated=updated_at
        )

        logger.info(f"Configuration updated for agent: {agent_id}")
        return response

    except HTTPException:
        raise
    except (ConfigurationError, AgentError) as e:
        logger.error(f"Configuration update failed for agent {agent_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error updating config for agent {agent_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Configuration update failed"
        )


@router.post("/bulk-create", response_model=List[CreateAgentResponse])
async def bulk_create_agents(req: BulkCreateAgentRequest):
    """
    Create multiple agents in bulk.

    Args:
        req: Bulk creation request containing list of agent specifications

    Returns:
        List of created agent responses

    Raises:
        HTTPException: If any agent creation fails
    """
    try:
        logger.info(f"Starting bulk creation of {len(req.agents)} agents")

        created_agents = []
        failed_agents = []

        for i, agent_req in enumerate(req.agents):
            try:
                # Create agent using factory
                agent = AgentFactory.create_agent(
                    agent_type=agent_req.agent_type,
                    config=agent_req.config,
                    name=agent_req.name
                )

                # Generate unique agent ID
                agent_id = agent_req.name or f"{agent_req.agent_type}_{uuid.uuid4().hex[:8]}"

                # Store agent in memory
                created_at = datetime.now(timezone.utc).isoformat()
                _active_agents[agent_id] = {
                    "agent": agent,
                    "created_at": created_at,
                    "type": agent_req.agent_type
                }

                response = CreateAgentResponse(
                    agent_id=agent_id,
                    agent_type=agent_req.agent_type,
                    status="created",
                    capabilities=agent.capabilities,
                    created_at=created_at
                )

                created_agents.append(response)
                logger.info(f"Agent {i+1}/{len(req.agents)} created successfully: {agent_id}")

            except Exception as e:
                error_msg = f"Failed to create agent {i+1}: {str(e)}"
                logger.error(error_msg)
                failed_agents.append({"index": i, "error": error_msg})

        if failed_agents:
            # If some agents failed, return partial success with error details
            logger.warning(f"Bulk creation completed with {len(failed_agents)} failures")
            raise HTTPException(
                status_code=status.HTTP_207_MULTI_STATUS,
                detail={
                    "message": f"Created {len(created_agents)} agents, {len(failed_agents)} failed",
                    "created": [agent.dict() for agent in created_agents],
                    "failed": failed_agents
                }
            )

        logger.info(f"Bulk creation completed successfully: {len(created_agents)} agents created")
        return created_agents

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Bulk creation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Bulk agent creation failed: {str(e)}"
        )


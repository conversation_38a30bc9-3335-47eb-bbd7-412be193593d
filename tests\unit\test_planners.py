"""
Unit tests for planners module.

This module contains comprehensive unit tests for all planner implementations
including MockPlanner, ReActPlanner, and base planner functionality.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from typing import List

from app.planners.base import Planner
from app.planners.mock_planner import MockPlanner
from app.planners.react_planner import ReActPlanner
from app.core.types import Message, Action, Plan, Observation
from app.core.exceptions import PlannerError


class TestMockPlanner:
    """Test cases for MockPlanner class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.planner = MockPlanner()
    
    def test_initialization(self):
        """Test MockPlanner initialization."""
        assert isinstance(self.planner, MockPlanner)
        assert hasattr(self.planner, 'plan')
    
    def test_plan_basic_functionality(self):
        """Test basic plan generation."""
        goal = "Calculate 2 + 3"
        history = [Message(role="user", content=goal)]
        state = {}
        
        plan = self.planner.plan(goal, history, state)
        
        assert isinstance(plan, Plan)
        assert isinstance(plan.actions, list)
        assert len(plan.actions) > 0
        
        # Check first action
        first_action = plan.actions[0]
        assert isinstance(first_action, Action)
        assert hasattr(first_action, 'tool')
        assert hasattr(first_action, 'parameters')
    
    def test_plan_math_goal(self):
        """Test plan generation for math-related goals."""
        math_goals = [
            "Calculate 5 * 7",
            "What is 100 / 4?",
            "Compute 2^8",
            "Find the square root of 16"
        ]
        
        for goal in math_goals:
            history = [Message(role="user", content=goal)]
            state = {}
            
            plan = self.planner.plan(goal, history, state)
            
            assert isinstance(plan, Plan)
            assert len(plan.actions) > 0
            
            # Should include math.eval tool for math goals
            math_actions = [action for action in plan.actions if action.tool == "math.eval"]
            assert len(math_actions) > 0
    
    def test_plan_http_goal(self):
        """Test plan generation for HTTP-related goals."""
        http_goals = [
            "Fetch data from httpbin.org",
            "Get the content of example.com",
            "Make a request to api.github.com"
        ]
        
        for goal in http_goals:
            history = [Message(role="user", content=goal)]
            state = {}
            
            plan = self.planner.plan(goal, history, state)
            
            assert isinstance(plan, Plan)
            assert len(plan.actions) > 0
            
            # Should include http.get tool for HTTP goals
            http_actions = [action for action in plan.actions if action.tool == "http.get"]
            assert len(http_actions) > 0
    
    def test_plan_combined_goal(self):
        """Test plan generation for goals requiring multiple tools."""
        goal = "Fetch httpbin.org and calculate 2+3"
        history = [Message(role="user", content=goal)]
        state = {}
        
        plan = self.planner.plan(goal, history, state)
        
        assert isinstance(plan, Plan)
        assert len(plan.actions) >= 2  # Should have multiple actions
        
        # Should include both tools
        tools_used = [action.tool for action in plan.actions]
        assert "http.get" in tools_used
        assert "math.eval" in tools_used
    
    def test_plan_with_history(self):
        """Test plan generation with conversation history."""
        goal = "Continue the calculation"
        history = [
            Message(role="user", content="Calculate 10 + 5"),
            Message(role="assistant", content="The result is 15"),
            Message(role="user", content=goal)
        ]
        state = {"previous_result": 15}
        
        plan = self.planner.plan(goal, history, state)
        
        assert isinstance(plan, Plan)
        assert len(plan.actions) > 0
    
    def test_plan_with_state(self):
        """Test plan generation with existing state."""
        goal = "Use the previous result"
        history = [Message(role="user", content=goal)]
        state = {
            "step_count": 3,
            "previous_calculations": [10, 20, 30],
            "context": "mathematical operations"
        }
        
        plan = self.planner.plan(goal, history, state)
        
        assert isinstance(plan, Plan)
        assert len(plan.actions) > 0
    
    def test_plan_empty_goal(self):
        """Test plan generation with empty goal."""
        goal = ""
        history = []
        state = {}
        
        plan = self.planner.plan(goal, history, state)
        
        # Should still return a valid plan, even if minimal
        assert isinstance(plan, Plan)
        assert isinstance(plan.actions, list)
    
    def test_plan_unknown_goal(self):
        """Test plan generation with unknown/unclear goal."""
        unclear_goals = [
            "Do something",
            "Help me",
            "I need assistance",
            "What should I do?"
        ]
        
        for goal in unclear_goals:
            history = [Message(role="user", content=goal)]
            state = {}
            
            plan = self.planner.plan(goal, history, state)
            
            assert isinstance(plan, Plan)
            assert isinstance(plan.actions, list)
            # May have empty actions for unclear goals
    
    def test_plan_deterministic_behavior(self):
        """Test that MockPlanner produces consistent results."""
        goal = "Calculate 2 + 2"
        history = [Message(role="user", content=goal)]
        state = {}
        
        # Generate multiple plans with same input
        plans = []
        for _ in range(5):
            plan = self.planner.plan(goal, history, state)
            plans.append(plan)
        
        # All plans should be similar (MockPlanner should be deterministic)
        first_plan = plans[0]
        for plan in plans[1:]:
            assert len(plan.actions) == len(first_plan.actions)
            # Actions should be the same type and tool
            for i, action in enumerate(plan.actions):
                assert action.tool == first_plan.actions[i].tool


class TestReActPlanner:
    """Test cases for ReActPlanner class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.mock_llm = Mock()
        self.allowed_tools = ["http.get", "math.eval"]
        self.planner = ReActPlanner(llm=self.mock_llm, allowed_tools=self.allowed_tools)
    
    def test_initialization(self):
        """Test ReActPlanner initialization."""
        assert self.planner.llm == self.mock_llm
        assert self.planner.allowed_tools == self.allowed_tools
    
    def test_initialization_without_allowed_tools(self):
        """Test ReActPlanner initialization without allowed tools."""
        planner = ReActPlanner(llm=self.mock_llm)
        assert planner.allowed_tools == []
    
    def test_plan_successful_generation(self):
        """Test successful plan generation."""
        goal = "Calculate 2 + 3"
        history = [Message(role="user", content=goal)]
        state = {}
        
        # Mock LLM response
        mock_response = Mock()
        mock_response.content = """
        I need to calculate 2 + 3.
        
        Action: math.eval
        Parameters: {"expression": "2 + 3"}
        """
        self.mock_llm.generate.return_value = mock_response
        
        plan = self.planner.plan(goal, history, state)
        
        assert isinstance(plan, Plan)
        assert len(plan.actions) > 0
        
        # Check that LLM was called
        self.mock_llm.generate.assert_called_once()
        
        # Check action details
        action = plan.actions[0]
        assert action.tool == "math.eval"
        assert "expression" in action.parameters
    
    def test_plan_with_multiple_actions(self):
        """Test plan generation with multiple actions."""
        goal = "Fetch httpbin.org and calculate 2+3"
        history = [Message(role="user", content=goal)]
        state = {}
        
        # Mock LLM response with multiple actions
        mock_response = Mock()
        mock_response.content = """
        I need to fetch data and do a calculation.
        
        Action: http.get
        Parameters: {"url": "https://httpbin.org/json"}
        
        Action: math.eval
        Parameters: {"expression": "2 + 3"}
        """
        self.mock_llm.generate.return_value = mock_response
        
        plan = self.planner.plan(goal, history, state)
        
        assert isinstance(plan, Plan)
        assert len(plan.actions) == 2
        
        assert plan.actions[0].tool == "http.get"
        assert plan.actions[1].tool == "math.eval"
    
    def test_plan_with_disallowed_tool(self):
        """Test plan generation when LLM suggests disallowed tool."""
        goal = "Delete a file"
        history = [Message(role="user", content=goal)]
        state = {}
        
        # Mock LLM response with disallowed tool
        mock_response = Mock()
        mock_response.content = """
        I need to delete a file.
        
        Action: file.delete
        Parameters: {"path": "/tmp/file.txt"}
        """
        self.mock_llm.generate.return_value = mock_response
        
        plan = self.planner.plan(goal, history, state)
        
        # Should filter out disallowed tools
        assert isinstance(plan, Plan)
        # Should have no actions since file.delete is not in allowed_tools
        assert len(plan.actions) == 0
    
    def test_plan_llm_error(self):
        """Test plan generation when LLM fails."""
        goal = "Test goal"
        history = [Message(role="user", content=goal)]
        state = {}
        
        # Mock LLM error
        self.mock_llm.generate.side_effect = Exception("LLM API Error")
        
        with pytest.raises(PlannerError) as exc_info:
            self.planner.plan(goal, history, state)
        
        assert "Failed to generate plan" in str(exc_info.value)
    
    def test_plan_invalid_llm_response(self):
        """Test plan generation with invalid LLM response format."""
        goal = "Test goal"
        history = [Message(role="user", content=goal)]
        state = {}
        
        # Mock invalid LLM response
        mock_response = Mock()
        mock_response.content = "This is not a valid action format"
        self.mock_llm.generate.return_value = mock_response
        
        plan = self.planner.plan(goal, history, state)
        
        # Should return empty plan for invalid format
        assert isinstance(plan, Plan)
        assert len(plan.actions) == 0
    
    def test_plan_with_conversation_history(self):
        """Test plan generation with conversation history."""
        goal = "Continue the task"
        history = [
            Message(role="user", content="Calculate 10 + 5"),
            Message(role="assistant", content="I'll calculate that for you."),
            Message(role="user", content=goal)
        ]
        state = {}
        
        mock_response = Mock()
        mock_response.content = """
        Based on the previous conversation, I'll continue.
        
        Action: math.eval
        Parameters: {"expression": "10 + 5"}
        """
        self.mock_llm.generate.return_value = mock_response
        
        plan = self.planner.plan(goal, history, state)
        
        assert isinstance(plan, Plan)
        assert len(plan.actions) > 0
        
        # Verify that history was included in LLM call
        call_args = self.mock_llm.generate.call_args
        messages = call_args[0][0]  # First argument should be messages
        assert len(messages) >= len(history)
    
    def test_plan_action_parsing(self):
        """Test parsing of different action formats."""
        test_cases = [
            # Standard format
            {
                "content": 'Action: math.eval\nParameters: {"expression": "2+2"}',
                "expected_tool": "math.eval",
                "expected_params": {"expression": "2+2"}
            },
            # JSON format
            {
                "content": '{"action": "http.get", "parameters": {"url": "example.com"}}',
                "expected_tool": "http.get",
                "expected_params": {"url": "example.com"}
            },
            # Mixed format
            {
                "content": 'Tool: math.eval\nArgs: {"expression": "5*5"}',
                "expected_tool": "math.eval",
                "expected_params": {"expression": "5*5"}
            }
        ]
        
        for case in test_cases:
            goal = "Test parsing"
            history = [Message(role="user", content=goal)]
            state = {}
            
            mock_response = Mock()
            mock_response.content = case["content"]
            self.mock_llm.generate.return_value = mock_response
            
            plan = self.planner.plan(goal, history, state)
            
            if case["expected_tool"] in self.allowed_tools:
                assert len(plan.actions) > 0
                action = plan.actions[0]
                assert action.tool == case["expected_tool"]
                # Parameters might be parsed differently, so just check they exist
                assert isinstance(action.parameters, dict)


class TestPlannerBase:
    """Test cases for base Planner functionality."""
    
    def test_planner_interface(self):
        """Test that Planner is an abstract base class."""
        # Should not be able to instantiate Planner directly
        with pytest.raises(TypeError):
            Planner()
    
    def test_custom_planner_implementation(self):
        """Test custom planner implementation."""
        class CustomPlanner(Planner):
            def plan(self, goal: str, history: List[Message], state: dict) -> Plan:
                # Simple custom planner that always returns one action
                action = Action(tool="custom_tool", parameters={"goal": goal})
                return Plan(actions=[action])
        
        planner = CustomPlanner()
        goal = "Test custom planner"
        history = [Message(role="user", content=goal)]
        state = {}
        
        plan = planner.plan(goal, history, state)
        
        assert isinstance(plan, Plan)
        assert len(plan.actions) == 1
        assert plan.actions[0].tool == "custom_tool"
        assert plan.actions[0].parameters["goal"] == goal


class TestPlannerIntegration:
    """Test cases for planner integration scenarios."""
    
    def test_planner_with_tool_registry(self):
        """Test planner integration with tool registry."""
        from app.tools.registry import ToolRegistry
        from app.tools.math_eval import math_eval
        
        registry = ToolRegistry()
        registry.register(math_eval)
        
        # MockPlanner should work with available tools
        planner = MockPlanner()
        goal = "Calculate 3 * 4"
        history = [Message(role="user", content=goal)]
        state = {}
        
        plan = planner.plan(goal, history, state)
        
        # Verify plan uses available tools
        tools_in_plan = [action.tool for action in plan.actions]
        available_tools = [tool.spec.name for tool in registry.list()]
        
        for tool in tools_in_plan:
            assert tool in available_tools or tool == "math.eval"  # MockPlanner knows about math.eval
    
    def test_planner_state_management(self):
        """Test planner state management across multiple calls."""
        planner = MockPlanner()
        
        # First planning call
        goal1 = "Calculate 5 + 5"
        history1 = [Message(role="user", content=goal1)]
        state1 = {}
        
        plan1 = planner.plan(goal1, history1, state1)
        
        # Second planning call with updated state
        goal2 = "Double the previous result"
        history2 = history1 + [
            Message(role="assistant", content="The result is 10"),
            Message(role="user", content=goal2)
        ]
        state2 = {"previous_result": 10, "step_count": 1}
        
        plan2 = planner.plan(goal2, history2, state2)
        
        # Both plans should be valid
        assert isinstance(plan1, Plan)
        assert isinstance(plan2, Plan)
        assert len(plan1.actions) > 0
        assert len(plan2.actions) > 0

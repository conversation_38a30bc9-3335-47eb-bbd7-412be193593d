from __future__ import annotations
import os, json
from typing import Any
from .base import LLM

class GeminiProvider(LLM):
    """
    Google Gemini provider via google-generativeai.

    Env:
      - GEMINI_API_KEY
      - GEMINI_MODEL (default: gemini-1.5-flash)

    Install:
      pip install "google-generativeai>=0.7.2"
    """
    def __init__(self, model_env: str = "GEMINI_MODEL", api_key_env: str = "GEMINI_API_KEY"):
        try:
            import google.generativeai as genai
        except Exception as e:
            raise RuntimeError("Install gemini: pip install 'google-generativeai>=0.7.2'") from e

        api_key = os.getenv(api_key_env)
        if not api_key:
            raise RuntimeError(f"{api_key_env} is not set")
        genai.configure(api_key=api_key)
        self.model_name = os.getenv(model_env, "gemini-1.5-flash")
        self._genai = genai

        # Recommended: bias model to JSON with a system instruction
        self._model = genai.GenerativeModel(
            self.model_name,
            system_instruction="Return STRICT JSON only. No markdown, no code fences."
        )

    def generate(self, prompt: str) -> str:
        # Gemini returns a rich object; .text is the concatenated string
        resp = self._model.generate_content(prompt)
        # Sometimes .text can be None if blocked; handle gracefully
        text = getattr(resp, "text", None)
        if not text:
            # try first candidate
            try:
                text = resp.candidates[0].content.parts[0].text
            except Exception:
                text = ""
        return text

    def parse_json(self, text: str) -> Any:
        try:
            return json.loads(text)
        except Exception:
            # Minimal rescue
            start, end = text.find("{"), text.rfind("}")
            if start != -1 and end != -1 and end > start:
                return json.loads(text[start:end+1])
            raise

# Memory Adapters Documentation

This document provides comprehensive technical documentation for the AgentKit memory system adapters.

## 🚨 Critical Fix: Memory Architecture Overhaul

**Problem Solved**: The original error `'InMemoryAdapter' object has no attribute 'add_message'` has been completely resolved through a comprehensive memory architecture redesign.

### Root Cause Analysis
The error occurred because:
1. **Interface Mismatch**: Routes were calling `add_message()` on adapters that only had generic `add()`, `get()`, `clear()` methods
2. **Missing Abstraction**: No unified interface between high-level memory operations and low-level storage
3. **Incomplete Integration**: Memory types in `base.py` weren't properly integrated with adapter implementations
4. **Inconsistent Method Signatures**: Different adapters had different method signatures and behaviors

### Solution: Unified Memory Interface

The memory system was completely redesigned with a unified adapter pattern:

```python
from abc import ABC, abstractmethod
from typing import Any, List, Dict, Optional
from app.core.types import Message
import time

class BaseMemoryAdapter(ABC):
    """Abstract base class providing unified interface for all memory adapters."""

    # Core storage methods (implemented by each adapter)
    @abstractmethod
    def add(self, key: str, item: Any) -> None:
        """Add an item to storage."""
        pass

    @abstractmethod
    def get(self, key: str, limit: Optional[int] = None) -> List[Any]:
        """Retrieve items from storage."""
        pass

    @abstractmethod
    def clear(self, key: str) -> None:
        """Clear items for a specific key."""
        pass

    # Unified interface methods (implemented in base class)
    def _build_key(self, agent_id: str, user_id: str, session_id: str = None) -> str:
        """Build consistent key format for storage."""
        if session_id:
            return f"{agent_id}:{user_id}:{session_id}"
        return f"{agent_id}:{user_id}"

    def add_message(self, agent_id: str, user_id: str, session_id: str, message: Message) -> None:
        """Add a message to memory with consistent format."""
        key = self._build_key(agent_id, user_id, session_id)
        message_data = {
            "role": message.role,
            "content": message.content,
            "timestamp": time.time(),
            "agent_id": agent_id,
            "user_id": user_id,
            "session_id": session_id
        }
        self.add(key, message_data)

    def get_messages(self, agent_id: str, user_id: str, session_id: str, limit: Optional[int] = None) -> List[Dict]:
        """Get messages from memory with consistent format."""
        key = self._build_key(agent_id, user_id, session_id)
        return self.get(key, limit)

    def log_event(self, event: Dict[str, Any]) -> None:
        """Log an episodic event with timestamp."""
        event_data = {
            **event,
            "timestamp": time.time(),
            "event_id": f"event_{int(time.time() * 1000)}"
        }
        self.add("events", event_data)

    def add_procedure(self, agent_id: str, user_id: str, procedure: Dict[str, Any]) -> None:
        """Add a procedure to memory."""
        key = f"{self._build_key(agent_id, user_id)}:procedures"
        procedure_data = {
            **procedure,
            "timestamp": time.time(),
            "agent_id": agent_id,
            "user_id": user_id
        }
        self.add(key, procedure_data)

    def add_fact(self, agent_id: str, user_id: str, fact: Dict[str, Any]) -> None:
        """Add a fact to semantic memory."""
        key = f"{self._build_key(agent_id, user_id)}:facts"
        fact_data = {
            **fact,
            "timestamp": time.time(),
            "agent_id": agent_id,
            "user_id": user_id
        }
        self.add(key, fact_data)
```

## Architecture Overview

The memory system now provides a unified interface across all storage backends:

```
┌─────────────────────────────────────────────────────────────┐
│                    Memory Manager                           │
│  - Manages all memory types                                 │
│  - Routes operations to appropriate adapters               │
│  - Provides unified API for agents                         │
├─────────────────┬─────────────────┬─────────────────┬───────┤
│  Short-term     │   Episodic      │   Procedural    │Semantic│
│   Memory        │    Memory       │    Memory       │Memory │
│                 │                 │                 │       │
│• Conversation   │• Event Logs     │• Workflows      │• Facts│
│• Session Context│• Interactions   │• Procedures     │• Search│
│• Temporary Data │• User Behavior  │• Step-by-step   │• Knowledge│
│                 │                 │                 │       │
│ Uses: add_message│ Uses: log_event │ Uses: add_procedure│ Uses: add_fact│
│       get_messages│       get_events│       get_procedure│       search_facts│
└─────────────────┴─────────────────┴─────────────────┴───────┘
         │                 │                 │           │
┌────────▼─────────────────▼─────────────────▼───────────▼────┐
│              BaseMemoryAdapter Interface                    │
│  add() │ get() │ clear() │ add_message() │ log_event() │... │
└─────────────────────────────────────────────────────────────┘
         │                 │                 │           │
┌────────▼─────────────────▼─────────────────▼───────────▼────┐
│                 Storage Adapters                            │
│ InMemory │ JSONL │ SQLite │ Redis │ VectorDB │ S3 │ MongoDB │
└─────────────────────────────────────────────────────────────┘
```

## Available Memory Adapters

### 1. InMemoryAdapter ✅ FIXED
**Purpose:** Fast, ephemeral storage for development/testing. Data is lost on restart.

**Fixed Issues:**
- ✅ Added `add_message()` method
- ✅ Added `get_messages()` method
- ✅ Added `log_event()` method
- ✅ Added `add_procedure()` method
- ✅ Added `add_fact()` method
- ✅ Thread-safe implementation with proper locking
- ✅ Consistent key formatting

**Integration:**
```python
from app.memory.adapters import InMemoryAdapter
adapter = InMemoryAdapter()

# Now works - no more 'add_message' attribute error!
message = Message(role="user", content="Hello")
adapter.add_message("agent1", "user1", "session1", message)
messages = adapter.get_messages("agent1", "user1", "session1")
```

**Configuration:** No environment variables required.

### 2. JSONLAdapter ✅ ENHANCED
**Purpose:** Simple file-based persistent storage using JSON Lines format.

**Enhancements:**
- ✅ Inherits from BaseMemoryAdapter
- ✅ All unified interface methods available
- ✅ Automatic file creation and management
- ✅ Append-only writes for crash safety
- ✅ Proper error handling and logging

**Integration:**
```python
from app.memory.adapters import JSONLAdapter
adapter = JSONLAdapter(filepath="./memory.jsonl")

# All methods now available
adapter.add_message("agent1", "user1", "session1", message)
adapter.log_event({"action": "user_login", "result": "success"})
```

**Configuration:**
- `MEMORY_JSONL_PATH` (optional): Path to JSONL file

### 3. SQLiteAdapter
**Purpose:** Lightweight, local database for persistent storage. Suitable for single-node deployments.
**Integration:**
```python
from app.memory.adapters_sqlite import SQLiteAdapter
adapter = SQLiteAdapter(db_path="./memory.sqlite")
```
**Configuration:**
- `MEMORY_SQLITE_PATH` (optional): Path to SQLite database file.

---

### 4. RedisAdapter
**Purpose:** Distributed, in-memory storage for scalable, multi-node deployments. Supports fast access and pub/sub.
**Integration:**
```python
from app.memory.adapters_redis import RedisAdapter
adapter = RedisAdapter(host="localhost", port=6379, db=0)
```
**Configuration:**
- `REDIS_HOST` (default: localhost)
- `REDIS_PORT` (default: 6379)
- `REDIS_DB` (default: 0)
- `REDIS_PASSWORD` (optional)

---

### 5. VectorDBAdapter (Qdrant)
**Purpose:** Semantic memory storage for vector embeddings, similarity search, and retrieval-augmented generation.
**Integration:**
```python
from app.memory.adapters_vectordb import VectorDBAdapter
adapter = VectorDBAdapter(host="localhost", port=6333, api_key=None)
```
**Configuration:**
- `QDRANT_HOST` (default: localhost)
- `QDRANT_PORT` (default: 6333)
- `QDRANT_API_KEY` (optional)

---

### 6. MongoDBAdapter
**Purpose:** Document-oriented, scalable storage for large datasets and flexible schema.
**Integration:**
```python
from app.memory.adapters_mongodb import MongoDBAdapter
adapter = MongoDBAdapter(uri="mongodb://localhost:27017", db_name="agentkit", collection="memory")
```
**Configuration:**
- `MONGODB_URI` (default: mongodb://localhost:27017)
- `MONGODB_DB` (default: agentkit)
- `MONGODB_COLLECTION` (default: memory)

---

### 7. PostgreSQLAdapter
**Purpose:** Relational, transactional storage for structured data and complex queries.
**Integration:**
```python
from app.memory.adapters_postgresql import PostgreSQLAdapter
adapter = PostgreSQLAdapter(dsn="dbname=agentkit user=postgres password=secret host=localhost", table="memory")
```
**Configuration:**
- `POSTGRES_DSN` (e.g., dbname=agentkit user=postgres password=secret host=localhost)
- `POSTGRES_TABLE` (default: memory)

---

### 8. S3Adapter
**Purpose:** Cloud object storage for large-scale, distributed persistence and backup.
**Integration:**
```python
from app.memory.adapters_s3 import S3Adapter
adapter = S3Adapter(bucket="my-agentkit-bucket", prefix="agent-memory/")
```
**Configuration:**
- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY`
- `AWS_REGION` (default: us-east-1)
- `S3_BUCKET` (required)
- `S3_PREFIX` (default: agent-memory/)

---

## Setup Instructions

1. **Install Required Packages:**
	- All adapters require their respective Python packages. These are listed in `pyproject.toml` and installed via `pip install -e .`.
	- For cloud adapters (S3, Qdrant), ensure network access and credentials are configured.

2. **Configure Environment Variables:**
	- Set environment variables in your `.env` file or system environment as needed for your chosen adapter.

3. **Select Adapter in Code:**
	- Import and instantiate the adapter class as shown above.
	- Pass configuration parameters from environment variables or config files.

4. **Run Migrations (if needed):**
	- For SQL adapters (SQLite, PostgreSQL), tables are auto-created if missing.

5. **Test Integration:**
	- Run your application and verify memory operations (save, load, delete, list_keys) work as expected.

---

## Adapter Selection Guidance

| Adapter         | Use Case                        | Persistence | Scalability | Cloud Ready | Notes                       |
|-----------------|----------------------------------|-------------|-------------|-------------|-----------------------------|
| InMemory        | Dev/test, ephemeral agents       | No          | No          | No          | Fast, but not persistent    |
| JSONL           | Small-scale, file-based          | Yes         | No          | No          | Simple, human-readable      |
| SQLite          | Local, single-node persistence   | Yes         | No          | No          | Lightweight, easy setup     |
| Redis           | Distributed, fast access         | Optional    | Yes         | Yes         | Requires Redis server       |
| VectorDB/Qdrant | Semantic search, RAG             | Yes         | Yes         | Yes         | For embeddings, similarity  |
| MongoDB         | Large, flexible datasets         | Yes         | Yes         | Yes         | Document-oriented           |
| PostgreSQL      | Structured, transactional data   | Yes         | Yes         | Yes         | Relational, robust          |
| S3              | Large-scale, backup, cloud       | Yes         | Yes         | Yes         | Object storage, pay-as-use  |

---

For further details, see the source code in the respective adapter files or contact the maintainers.

from __future__ import annotations
from typing import Protocol, Any, List
from abc import ABC, abstractmethod
import json

from ..core.types import Message, LLMResponse


class LLM(Protocol):
    def generate(self, prompt: str) -> str: ...
    def parse_json(self, text: str) -> Any:
        return json.loads(text)


class LL<PERSON><PERSON>ider(ABC):
    """
    Abstract base class for LLM providers.

    This class defines the interface that all LLM providers must implement
    to integrate with the agentic AI framework.
    """

    @abstractmethod
    def generate(self, messages: List[Message], **kwargs) -> LLMResponse:
        """
        Generate a response from the LLM.

        Args:
            messages: List of conversation messages
            **kwargs: Additional parameters for generation

        Returns:
            LLM response with content and metadata
        """
        pass

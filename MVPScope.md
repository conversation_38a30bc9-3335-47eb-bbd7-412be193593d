🗺️ Phased Roadmap (framework-first, app-ready)
Phase 0 – Skeleton (2–3 days)

Core types (Message, Action, Plan, Observation).

Simple AgentRunner loop.

ToolRegistry + 2 sample tools (http.get, math.eval).

In-memory Memory + JSONL run-store.

OpenAI adapter + JSON-structured responses (function calling/JSON mode).

FastAPI /agent/run.

Deliverable: examples/react_agent.py that searches the web and composes an answer with citations.

Phase 1 – Reliability & Controls (1 week)

Validators: schema check, numeric sanity (no negative RSI), null-guard.

Policies: YAML parser; enforce allowed_tools, max_steps, budget.

Retries/backoff, timeouts, circuit-breaker per tool.

Observability: step logs, run_id, cost accounting, metrics.

Deliverable: run dashboard (simple HTML) + JSONL trace per run.

Phase 2 – Memory & RAG (1 week)

Vector memory adapter (Chroma for MVP).

Write policies (what to store), summarization of long context.

Context composer (RAG chunks + scratchpad + instructions).

Cached tool results (semantic cache).

Deliverable: examples/rag_research_agent.py (docs → grounded answer).

Phase 3 – Better Planning & Graphs (1–2 weeks)

Planners: ReAct, Plan-Execute, LangGraph-like DAG runner (nodes/edges, state).

Heuristics: cost-aware tool selection, “fast path” rules.

Checkpoints + human approval hooks.

Deliverable: orchestrators/graph_runner.py + examples/multi_step_workflow.py.

Phase 4 – Multi-Agent & Interfaces (1–2 weeks)

OrchestratorAgent coordinating specialists (Research, Analyzer, Coder).

Slack/Telegram connectors; optional voice I/O.

RBAC and per-role policies; secrets vault adapter.

Deliverable: examples/stock_analyst_suite/ using NewsAgent, TechnicalAgent, FundamentalAgent.

🧰 Extensibility Playbook (how you’ll add power later)

LLM Providers: Add new adapters by implementing generate() and structured_generate(); swap via env LLM_PROVIDER.

Tools: Each tool is a self-contained folder with spec.yaml + tool.py; registry auto-loads at startup.

Planners: New planner class that satisfies Planner; hot-swap per agent in config.

Policies: New YAMLs per domain; add “confirm_before” keys to require approvals.

Memory: Plug a new backend (Weaviate/Pinecone) without touching runner.

Validation: Drop-in validators (PII/tone/toxicity), or domain-specific assertions.

🧪 Testing, Eval, and “No-BS” Quality

Unit tests: planner output schema, tool adapters, validators.

Golden prompts: “smoke” tasks; assert JSON structure + key facts present.

Offline eval: small harness scoring groundedness, exactness, latency, cost.

Canary policy: lower budgets + tighter approvals in prod; flip to relaxed in dev.

Observability: every step emits run_id, step, tool, tokens, cost, latency.
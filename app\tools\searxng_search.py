"""
SearxNG Web Search Tool

This tool provides web search capabilities using a local SearxNG instance.
SearxNG is a privacy-respecting metasearch engine that aggregates results
from multiple search engines without tracking users.
"""

import os
import json
import requests
from typing import Dict, Any, List, Optional
from loguru import logger

from .base import Tool, ToolSpec


class SearxNGSearchTool(Tool):
    """
    Web search tool using SearxNG metasearch engine.
    
    Provides comprehensive web search capabilities with privacy protection
    and aggregated results from multiple search engines.
    """
    
    def __init__(self, searxng_url: Optional[str] = None):
        """
        Initialize SearxNG search tool.
        
        Args:
            searxng_url: URL of the SearxNG instance (defaults to localhost:8080)
        """
        self.searxng_url = searxng_url or os.getenv("SEARXNG_URL", "http://localhost:8089")
        
        # Ensure URL doesn't end with slash
        self.searxng_url = self.searxng_url.rstrip('/')
        
        # Define tool specification
        self.spec = ToolSpec(
            name="searxng_search",
            description="Search the web using SearxNG metasearch engine for comprehensive results",
            input_schema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query to execute"
                    },
                    "categories": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Search categories (general, images, videos, news, map, music, it, science, files)",
                        "default": ["general"]
                    },
                    "engines": {
                        "type": "array", 
                        "items": {"type": "string"},
                        "description": "Specific search engines to use (optional)",
                        "default": []
                    },
                    "language": {
                        "type": "string",
                        "description": "Language code for search results (e.g., 'en', 'es', 'fr')",
                        "default": "en"
                    },
                    "time_range": {
                        "type": "string",
                        "description": "Time range for results (day, week, month, year)",
                        "default": ""
                    },
                    "max_results": {
                        "type": "integer",
                        "description": "Maximum number of results to return",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 50
                    }
                },
                "required": ["query"]
            },
            output_schema={
                "type": "object",
                "properties": {
                    "results": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "title": {"type": "string"},
                                "url": {"type": "string"},
                                "content": {"type": "string"},
                                "engine": {"type": "string"},
                                "score": {"type": "number"}
                            }
                        }
                    },
                    "query": {"type": "string"},
                    "total_results": {"type": "integer"},
                    "search_time": {"type": "number"},
                    "engines_used": {"type": "array", "items": {"type": "string"}}
                }
            }
        )
        
        logger.info(f"SearxNG search tool initialized with URL: {self.searxng_url}")
    
    def execute(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute web search using SearxNG.
        
        Args:
            parameters: Search parameters including query, categories, etc.
            
        Returns:
            Dictionary containing search results and metadata
            
        Raises:
            RuntimeError: If SearxNG is not accessible or search fails
        """
        try:
            # Validate required parameters
            if "query" not in parameters:
                raise ValueError("Query parameter is required")
            
            query = parameters["query"].strip()
            if not query:
                raise ValueError("Query cannot be empty")
            
            # Prepare search parameters
            search_params = {
                "q": query,
                "format": "json",
                "language": parameters.get("language", "en"),
                "categories": ",".join(parameters.get("categories", ["general"])),
                "pageno": 1
            }
            
            # Add optional parameters
            if parameters.get("engines"):
                search_params["engines"] = ",".join(parameters["engines"])
            
            if parameters.get("time_range"):
                search_params["time_range"] = parameters["time_range"]
            
            logger.debug(f"Executing SearxNG search: {query}")
            
            # Make search request
            response = requests.get(
                f"{self.searxng_url}/search",
                params=search_params,
                timeout=30,
                headers={
                    "User-Agent": "AgentKit-SearxNG-Tool/1.0",
                    "Accept": "application/json"
                }
            )
            
            response.raise_for_status()
            
            # Parse response
            search_data = response.json()
            
            # Extract results
            results = []
            max_results = parameters.get("max_results", 10)
            
            for result in search_data.get("results", [])[:max_results]:
                results.append({
                    "title": result.get("title", ""),
                    "url": result.get("url", ""),
                    "content": result.get("content", ""),
                    "engine": result.get("engine", ""),
                    "score": result.get("score", 0.0)
                })
            
            # Get engines used
            engines_used = list(set(result.get("engine", "") for result in search_data.get("results", [])))
            
            search_result = {
                "results": results,
                "query": query,
                "total_results": len(results),
                "search_time": search_data.get("number_of_results", 0),
                "engines_used": engines_used
            }
            
            logger.info(f"SearxNG search completed: {len(results)} results for '{query}'")
            return search_result
            
        except requests.exceptions.ConnectionError as e:
            error_msg = (
                f"Cannot connect to SearxNG at {self.searxng_url}. "
                f"Please ensure SearxNG is running locally. "
                f"Install with: docker run -d -p 8080:8080 searxng/searxng"
            )
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e
            
        except requests.exceptions.Timeout as e:
            error_msg = f"SearxNG search timed out for query: {query}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e
            
        except requests.exceptions.HTTPError as e:
            error_msg = f"SearxNG HTTP error {response.status_code}: {response.text}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e
            
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON response from SearxNG: {str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e
            
        except Exception as e:
            error_msg = f"SearxNG search failed: {str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e
    
    def health_check(self) -> bool:
        """
        Check if SearxNG instance is accessible and healthy.
        
        Returns:
            True if SearxNG is accessible, False otherwise
        """
        try:
            response = requests.get(
                f"{self.searxng_url}/stats",
                timeout=5,
                headers={"User-Agent": "AgentKit-SearxNG-Tool/1.0"}
            )
            return response.status_code == 200
        except Exception as e:
            logger.warning(f"SearxNG health check failed: {str(e)}")
            return False


# Create tool instance
searxng_search = SearxNGSearchTool()

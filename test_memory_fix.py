#!/usr/bin/env python3
"""
Test script to verify memory architecture fixes.
"""

def test_imports():
    """Test that all memory components can be imported."""
    try:
        from app.memory.base import MemoryManager, ShortTermMemory, EpisodicMemory
        print("✓ Memory base classes imported successfully")
        
        from app.memory.adapters import BaseMemoryAdapter, InMemoryAdapter, JSONLAdapter
        print("✓ Memory adapters imported successfully")
        
        from app.agents.base import BaseAgent
        print("✓ BaseAgent imported successfully")
        
        return True
    except Exception as e:
        print(f"✗ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_manager():
    """Test MemoryManager functionality."""
    try:
        from app.memory.base import MemoryManager
        from app.memory.adapters import InMemoryAdapter
        from app.core.types import Message
        
        # Create memory manager with adapters
        adapters = {
            "short_term": InMemoryAdapter(),
            "episodic": InMemoryAdapter(),
            "procedural": InMemoryAdapter(),
            "semantic": InMemoryAdapter()
        }
        
        manager = MemoryManager(adapters=adapters)
        print("✓ MemoryManager created successfully")
        
        # Test adding a message
        message = Message(role="user", content="Hello, world!")
        manager.add_short_term_message("agent1", "user1", "session1", message)
        print("✓ Message added to short-term memory")
        
        # Test retrieving messages
        messages = manager.get_short_term_messages("agent1", "user1", "session1")
        print(f"✓ Retrieved {len(messages)} messages from short-term memory")
        
        # Test logging episode
        manager.log_episode("agent1", "user1", "session1", {
            "action": "test_action",
            "result": "success"
        })
        print("✓ Episode logged successfully")
        
        return True
    except Exception as e:
        print(f"✗ MemoryManager test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_creation():
    """Test agent creation with memory system."""
    try:
        from app.agents.base import BaseAgent
        
        # Create agent with memory configuration
        memory_config = {
            "short_term": {"adapter": "in_memory"},
            "episodic": {"adapter": "in_memory"},
            "procedural": {"adapter": "in_memory"},
            "semantic": {"adapter": "in_memory"}
        }
        
        agent = BaseAgent(
            name="TestAgent",
            config={
                "memory": memory_config,
                "tools": [],
                "planner": {}
            }
        )
        
        print("✓ Agent created successfully")
        
        # Check if memory manager exists
        if hasattr(agent, 'memory_manager'):
            print("✓ Agent has memory_manager attribute")
        else:
            print("✗ Agent missing memory_manager attribute")
            return False
        
        # Check if memory dict exists for backward compatibility
        if hasattr(agent, 'memory') and isinstance(agent.memory, dict):
            print("✓ Agent has memory dict for backward compatibility")
        else:
            print("✗ Agent missing memory dict")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Agent creation test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("Testing Memory Architecture Fixes")
    print("=" * 40)
    
    tests = [
        ("Import Tests", test_imports),
        ("MemoryManager Tests", test_memory_manager),
        ("Agent Creation Tests", test_agent_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 20)
        if test_func():
            passed += 1
            print(f"✓ {test_name} PASSED")
        else:
            print(f"✗ {test_name} FAILED")
    
    print("\n" + "=" * 40)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Memory architecture fixes are working.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    main()

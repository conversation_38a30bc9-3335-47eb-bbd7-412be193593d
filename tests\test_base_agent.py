"""
Unit tests for BaseAgent class.
"""

import pytest
from unittest.mock import Mock, patch
from app.agents.base import BaseAgent
from app.core.exceptions import AgentError, ConfigurationError


class TestAgent(BaseAgent):
    """Test implementation of BaseAgent for testing."""
    
    def process(self, query: str, context: dict) -> dict:
        return {"response": f"Processed: {query}", "test": True}


class TestBaseAgent:
    """Test cases for BaseAgent class."""
    
    def test_agent_initialization_success(self):
        """Test successful agent initialization."""
        config = {"tools": ["test_tool"], "memory": {"type": "buffer"}}
        agent = TestAgent("test_agent", config)
        
        assert agent.name == "test_agent"
        assert agent.config == config
        assert agent.capabilities == []
        assert agent._is_initialized is True
    
    def test_agent_initialization_invalid_name(self):
        """Test agent initialization with invalid name."""
        with pytest.raises(ConfigurationError, match="Agent name must be a non-empty string"):
            TestAgent("", {})
        
        with pytest.raises(ConfigurationError, match="Agent name must be a non-empty string"):
            TestAgent(None, {})
    
    def test_agent_initialization_invalid_config(self):
        """Test agent initialization with invalid config."""
        with pytest.raises(ConfigurationError, match="Agent config must be a dictionary"):
            TestAgent("test", "invalid_config")
    
    def test_add_capability(self):
        """Test adding capabilities to agent."""
        agent = TestAgent("test", {})
        
        agent.add_capability("test_capability")
        assert "test_capability" in agent.capabilities

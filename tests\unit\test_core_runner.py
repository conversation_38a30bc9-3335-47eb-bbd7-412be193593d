"""
Unit tests for Agent<PERSON>unner core module.

This module contains comprehensive unit tests for the AgentRunner class,
testing agent execution, state management, and error handling.
"""

import pytest
import uuid
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from app.core.runner import AgentRunner
from app.core.types import Message, Action, Plan, Observation
from app.core.exceptions import <PERSON><PERSON>rror, ValidationError
from app.tools.registry import ToolRegistry
from app.tools.math_eval import math_eval
from app.memory.buffer_memory import InMemoryBuffer
from app.validation.guards import StepLimitValidator, TokenBudgetValidator
from app.planners.mock_planner import MockPlanner


class TestAgentRunner:
    """Test cases for AgentRunner class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.registry = ToolRegistry()
        self.registry.register(math_eval)
        self.memory = InMemoryBuffer()
        self.validators = [StepLimitValidator(max_steps=5)]
        self.policy = {"allowed_tools": ["math.eval"], "max_steps": 5}
        self.planner = MockPlanner()
        
        self.runner = AgentRunner(
            planner=self.planner,
            tools=self.registry,
            memory=self.memory,
            validators=self.validators,
            policy=self.policy
        )
    
    def test_initialization(self):
        """Test AgentRunner initialization."""
        assert self.runner.planner == self.planner
        assert self.runner.tools == self.registry
        assert self.runner.memory == self.memory
        assert self.runner.validators == self.validators
        assert self.runner.policy == self.policy
    
    def test_run_successful_execution(self):
        """Test successful agent run execution."""
        run_id = str(uuid.uuid4())
        goal = "Calculate 2 + 3"
        history = [Message(role="user", content=goal)]
        state = {}
        
        result = self.runner.run(run_id, goal, history, state)
        
        assert "state" in result
        assert "history" in result
        assert "finish" in result
        assert isinstance(result["history"], list)
    
    def test_run_with_step_limit_exceeded(self):
        """Test run execution when step limit is exceeded."""
        # Create a validator with very low step limit
        strict_validators = [StepLimitValidator(max_steps=1)]
        strict_runner = AgentRunner(
            planner=self.planner,
            tools=self.registry,
            memory=self.memory,
            validators=strict_validators,
            policy={"allowed_tools": ["math.eval"], "max_steps": 1}
        )
        
        run_id = str(uuid.uuid4())
        goal = "Complex multi-step calculation"
        history = [Message(role="user", content=goal)]
        state = {}
        
        result = strict_runner.run(run_id, goal, history, state)
        
        # Should complete but with step limit reached
        assert "state" in result
        assert result["finish"] is True
    
    def test_run_with_validation_error(self):
        """Test run execution with validation error."""
        # Mock a validator that always fails
        failing_validator = Mock()
        failing_validator.validate.side_effect = ValidationError("Validation failed")
        
        failing_runner = AgentRunner(
            planner=self.planner,
            tools=self.registry,
            memory=self.memory,
            validators=[failing_validator],
            policy=self.policy
        )
        
        run_id = str(uuid.uuid4())
        goal = "Test goal"
        history = [Message(role="user", content=goal)]
        state = {}
        
        with pytest.raises(ValidationError):
            failing_runner.run(run_id, goal, history, state)
    
    def test_run_with_tool_execution_error(self):
        """Test run execution when tool execution fails."""
        # Mock a tool that fails
        failing_tool = Mock()
        failing_tool.spec.name = "failing_tool"
        failing_tool.execute.side_effect = Exception("Tool execution failed")
        
        failing_registry = ToolRegistry()
        failing_registry.register(failing_tool)
        
        failing_runner = AgentRunner(
            planner=self.planner,
            tools=failing_registry,
            memory=self.memory,
            validators=self.validators,
            policy={"allowed_tools": ["failing_tool"], "max_steps": 5}
        )
        
        run_id = str(uuid.uuid4())
        goal = "Use failing tool"
        history = [Message(role="user", content=goal)]
        state = {}
        
        # Should handle tool failure gracefully
        result = failing_runner.run(run_id, goal, history, state)
        assert "state" in result
    
    def test_run_with_memory_storage(self):
        """Test that run results are stored in memory."""
        run_id = str(uuid.uuid4())
        goal = "Test memory storage"
        history = [Message(role="user", content=goal)]
        state = {}
        
        result = self.runner.run(run_id, goal, history, state)
        
        # Check that memory was updated
        stored_messages = self.memory.get_messages(run_id)
        assert len(stored_messages) > 0
        assert any(msg.content == goal for msg in stored_messages)
    
    def test_run_with_empty_history(self):
        """Test run execution with empty history."""
        run_id = str(uuid.uuid4())
        goal = "Test with empty history"
        history = []
        state = {}
        
        result = self.runner.run(run_id, goal, history, state)
        
        assert "state" in result
        assert "history" in result
        assert len(result["history"]) >= 1  # Should have at least the goal message
    
    def test_run_with_existing_state(self):
        """Test run execution with existing state."""
        run_id = str(uuid.uuid4())
        goal = "Continue from existing state"
        history = [Message(role="user", content="Previous message")]
        state = {"previous_calculation": 42, "step_count": 2}
        
        result = self.runner.run(run_id, goal, history, state)
        
        assert "state" in result
        # State should be preserved or updated
        assert isinstance(result["state"], dict)
    
    def test_policy_enforcement(self):
        """Test that policy restrictions are enforced."""
        # Create runner with restricted policy
        restricted_policy = {"allowed_tools": [], "max_steps": 1}
        restricted_runner = AgentRunner(
            planner=self.planner,
            tools=self.registry,
            memory=self.memory,
            validators=self.validators,
            policy=restricted_policy
        )
        
        run_id = str(uuid.uuid4())
        goal = "Try to use restricted tools"
        history = [Message(role="user", content=goal)]
        state = {}
        
        result = restricted_runner.run(run_id, goal, history, state)
        
        # Should complete but with restrictions applied
        assert "state" in result
        assert result["finish"] is True
    
    def test_multiple_validators(self):
        """Test execution with multiple validators."""
        multi_validators = [
            StepLimitValidator(max_steps=10),
            TokenBudgetValidator(max_tokens=1000)
        ]
        
        multi_runner = AgentRunner(
            planner=self.planner,
            tools=self.registry,
            memory=self.memory,
            validators=multi_validators,
            policy=self.policy
        )
        
        run_id = str(uuid.uuid4())
        goal = "Test multiple validators"
        history = [Message(role="user", content=goal)]
        state = {}
        
        result = multi_runner.run(run_id, goal, history, state)
        
        assert "state" in result
        assert "history" in result
    
    def test_run_id_uniqueness(self):
        """Test that different run_ids maintain separate contexts."""
        goal = "Test run ID separation"
        history = [Message(role="user", content=goal)]
        state = {}
        
        run_id_1 = str(uuid.uuid4())
        run_id_2 = str(uuid.uuid4())
        
        result_1 = self.runner.run(run_id_1, goal, history, state)
        result_2 = self.runner.run(run_id_2, goal, history, state)
        
        # Both should succeed independently
        assert "state" in result_1
        assert "state" in result_2
        
        # Memory should maintain separate contexts
        messages_1 = self.memory.get_messages(run_id_1)
        messages_2 = self.memory.get_messages(run_id_2)
        
        # Should have separate message histories
        assert len(messages_1) > 0
        assert len(messages_2) > 0


class TestAgentRunnerErrorHandling:
    """Test error handling in AgentRunner."""
    
    def test_invalid_planner(self):
        """Test initialization with invalid planner."""
        with pytest.raises(TypeError):
            AgentRunner(
                planner=None,
                tools=ToolRegistry(),
                memory=InMemoryBuffer(),
                validators=[],
                policy={}
            )
    
    def test_invalid_tools_registry(self):
        """Test initialization with invalid tools registry."""
        with pytest.raises(TypeError):
            AgentRunner(
                planner=MockPlanner(),
                tools=None,
                memory=InMemoryBuffer(),
                validators=[],
                policy={}
            )
    
    def test_invalid_memory(self):
        """Test initialization with invalid memory."""
        with pytest.raises(TypeError):
            AgentRunner(
                planner=MockPlanner(),
                tools=ToolRegistry(),
                memory=None,
                validators=[],
                policy={}
            )
    
    def test_planner_failure(self):
        """Test handling of planner failures."""
        failing_planner = Mock()
        failing_planner.plan.side_effect = Exception("Planner failed")
        
        failing_runner = AgentRunner(
            planner=failing_planner,
            tools=ToolRegistry(),
            memory=InMemoryBuffer(),
            validators=[],
            policy={}
        )
        
        run_id = str(uuid.uuid4())
        goal = "Test planner failure"
        history = [Message(role="user", content=goal)]
        state = {}
        
        # Should handle planner failure gracefully
        with pytest.raises(Exception):
            failing_runner.run(run_id, goal, history, state)

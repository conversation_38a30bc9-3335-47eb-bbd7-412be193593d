"""
Unit tests for specialized agents.

This module contains comprehensive unit tests for all specialized agent
implementations including CustomerSupportAgent, SalesAgent, and ResearchAgent.
"""

import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime

from app.agents.customer_support import CustomerSupportAgent
from app.agents.sales import SalesAgent
from app.agents.research import ResearchAgent
from app.core.exceptions import AgentError, ConfigurationError


class TestCustomerSupportAgent:
    """Test cases for CustomerSupportAgent."""
    
    def test_initialization_default_config(self):
        """Test customer support agent initialization with default config."""
        config = {}
        agent = CustomerSupportAgent(config)
        
        assert agent.name == "customer_support"
        assert agent._is_initialized is True
        assert "ticket_management" in agent.capabilities
        assert "knowledge_base_search" in agent.capabilities
        assert "escalation_handling" in agent.capabilities
        assert agent.escalation_threshold == 0.3  # default value
    
    def test_initialization_custom_config(self):
        """Test customer support agent initialization with custom config."""
        config = {
            "escalation_threshold": 0.5,
            "max_resolution_attempts": 5,
            "supported_languages": ["en", "es"]
        }
        agent = CustomerSupportAgent(config)
        
        assert agent.escalation_threshold == 0.5
        assert agent.max_resolution_attempts == 5
        assert agent.supported_languages == ["en", "es"]
    
    def test_process_refund_request(self):
        """Test processing refund request."""
        agent = CustomerSupportAgent({})
        query = "I want a refund for my recent purchase"
        context = {"customer_id": "cust_123"}
        
        result = agent.process(query, context)
        
        assert "response" in result
        assert result["intent"] == "refund_request"
        assert "ticket" in result
        assert result["ticket"]["priority"] == "medium"
        assert isinstance(result["escalation_required"], bool)
    
    def test_process_technical_issue(self):
        """Test processing technical issue."""
        agent = CustomerSupportAgent({})
        query = "The app is not working and showing errors"
        context = {"customer_id": "cust_456"}
        
        result = agent.process(query, context)
        
        assert result["intent"] == "technical_issue"
        assert result["ticket"]["priority"] == "high"
        assert "troubleshoot" in result["response"].lower()
    
    def test_sentiment_analysis_negative(self):
        """Test sentiment analysis for negative query."""
        agent = CustomerSupportAgent({})
        query = "I'm very frustrated and angry with this terrible service"
        
        sentiment = agent._analyze_sentiment(query)
        
        assert sentiment["sentiment"] == "negative"
        assert sentiment["score"] < 0
        assert sentiment["confidence"] > 0
    
    def test_sentiment_analysis_positive(self):
        """Test sentiment analysis for positive query."""
        agent = CustomerSupportAgent({})
        query = "I love this product, it's amazing and excellent"
        
        sentiment = agent._analyze_sentiment(query)
        
        assert sentiment["sentiment"] == "positive"
        assert sentiment["score"] > 0
    
    def test_escalation_decision_negative_sentiment(self):
        """Test escalation decision based on negative sentiment."""
        agent = CustomerSupportAgent({"escalation_threshold": 0.3})
        sentiment = {"score": -0.5, "sentiment": "negative"}
        intent = "general_inquiry"
        context = {}
        
        should_escalate = agent._should_escalate(sentiment, intent, context)
        
        assert should_escalate is True
    
    def test_escalation_decision_complex_intent(self):
        """Test escalation decision based on complex intent."""
        agent = CustomerSupportAgent({})
        sentiment = {"score": 0.1, "sentiment": "neutral"}
        intent = "refund_request"
        context = {}
        
        should_escalate = agent._should_escalate(sentiment, intent, context)
        
        assert should_escalate is True
    
    def test_ticket_creation(self):
        """Test ticket creation for new query."""
        agent = CustomerSupportAgent({})
        query = "Need help with billing"
        intent = "billing_inquiry"
        customer_id = "cust_789"
        
        ticket = agent._handle_ticket(query, intent, customer_id, None)
        
        assert ticket["status"] == "created"
        assert ticket["customer_id"] == customer_id
        assert ticket["priority"] == "high"
        assert "ticket_id" in ticket
    
    def test_ticket_update(self):
        """Test ticket update for existing ticket."""
        agent = CustomerSupportAgent({})
        query = "Follow up on my previous issue"
        intent = "general_inquiry"
        existing_ticket_id = "***********-1234"
        
        ticket = agent._handle_ticket(query, intent, None, existing_ticket_id)
        
        assert ticket["status"] == "updated"
        assert ticket["ticket_id"] == existing_ticket_id


class TestSalesAgent:
    """Test cases for SalesAgent."""
    
    def test_initialization_default_config(self):
        """Test sales agent initialization with default config."""
        config = {}
        agent = SalesAgent(config)
        
        assert agent.name == "sales"
        assert agent._is_initialized is True
        assert "lead_qualification" in agent.capabilities
        assert "product_recommendation" in agent.capabilities
        assert agent.qualification_threshold == 0.7  # default value
        assert agent.max_discount == 0.15  # default value
    
    def test_initialization_custom_config(self):
        """Test sales agent initialization with custom config."""
        config = {
            "qualification_threshold": 0.8,
            "max_discount": 0.2,
            "product_catalog": ["prod1", "prod2"]
        }
        agent = SalesAgent(config)
        
        assert agent.qualification_threshold == 0.8
        assert agent.max_discount == 0.2
        assert agent.product_catalog == ["prod1", "prod2"]
    
    def test_process_pricing_inquiry(self):
        """Test processing pricing inquiry."""
        agent = SalesAgent({})
        query = "What are your pricing options?"
        context = {"customer_id": "cust_123", "budget": 5000}
        
        result = agent.process(query, context)
        
        assert result["intent"] == "pricing_inquiry"
        assert "pricing" in result
        assert "product_recommendations" in result
        assert result["lead_qualification"]["score"] > 0
    
    def test_process_demo_request(self):
        """Test processing demo request."""
        agent = SalesAgent({})
        query = "Can I get a demo of your product?"
        context = {"decision_maker": True}
        
        result = agent.process(query, context)
        
        assert result["intent"] == "demo_request"
        assert "demo" in result["response"].lower()
        assert "Schedule demo" in result["next_steps"]
    
    def test_lead_qualification_high_budget(self):
        """Test lead qualification with high budget."""
        agent = SalesAgent({})
        query = "I need a solution for my company"
        context = {"budget": 15000, "decision_maker": True}
        
        qualification = agent._qualify_lead(query, context)
        
        assert qualification["level"] == "high"
        assert qualification["score"] >= agent.qualification_threshold
        assert qualification["factors"]["budget"] is True
        assert qualification["factors"]["authority"] is True
    
    def test_lead_qualification_low_budget(self):
        """Test lead qualification with low budget."""
        agent = SalesAgent({})
        query = "Looking for a cheap solution"
        context = {"budget": 100}
        
        qualification = agent._qualify_lead(query, context)
        
        assert qualification["level"] in ["low", "medium"]
        assert qualification["factors"]["budget"] is True
    
    def test_product_recommendations(self):
        """Test product recommendations."""
        agent = SalesAgent({})
        query = "What products do you offer?"
        context = {}
        qualification = {"level": "high", "score": 0.8}
        
        recommendations = agent._recommend_products(query, context, qualification)
        
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        for rec in recommendations:
            assert "product_id" in rec
            assert "name" in rec
            assert "price" in rec
            assert "match_score" in rec
    
    def test_pricing_calculation_with_discounts(self):
        """Test pricing calculation with discounts."""
        agent = SalesAgent({"max_discount": 0.2})
        recommendations = [{"price": 100.0}]
        context = {"volume_discount": True, "annual_payment": True}
        
        pricing = agent._calculate_pricing(recommendations, context)
        
        assert pricing["base_price"] == 100.0
        assert pricing["discount_percentage"] > 0
        assert pricing["final_price"] < pricing["base_price"]
        assert pricing["discount_percentage"] <= agent.max_discount
    
    def test_sales_stage_determination(self):
        """Test sales stage determination."""
        agent = SalesAgent({})
        
        # Test negotiation stage
        stage = agent._determine_sales_stage("purchase_intent", {"level": "high"})
        assert stage == "negotiation"
        
        # Test proposal stage
        stage = agent._determine_sales_stage("demo_request", {"level": "medium"})
        assert stage == "proposal"
        
        # Test lead stage
        stage = agent._determine_sales_stage("general_sales_inquiry", {"level": "low"})
        assert stage == "lead"


class TestResearchAgent:
    """Test cases for ResearchAgent."""
    
    def test_initialization_default_config(self):
        """Test research agent initialization with default config."""
        config = {}
        agent = ResearchAgent(config)
        
        assert agent.name == "research"
        assert agent._is_initialized is True
        assert "information_gathering" in agent.capabilities
        assert "fact_verification" in agent.capabilities
        assert agent.max_sources == 10  # default value
        assert agent.fact_check_threshold == 0.8  # default value
    
    def test_initialization_custom_config(self):
        """Test research agent initialization with custom config."""
        config = {
            "max_sources": 15,
            "fact_check_threshold": 0.9,
            "research_depth": "deep",
            "citation_style": "mla"
        }
        agent = ResearchAgent(config)
        
        assert agent.max_sources == 15
        assert agent.fact_check_threshold == 0.9
        assert agent.research_depth == "deep"
        assert agent.citation_style == "mla"
    
    def test_process_research_query(self):
        """Test processing research query."""
        agent = ResearchAgent({})
        query = "What are the latest trends in artificial intelligence?"
        context = {"research_type": "trend_analysis"}
        
        result = agent.process(query, context)
        
        assert "research_summary" in result
        assert "detailed_findings" in result
        assert "sources" in result
        assert "citations" in result
        assert "confidence_score" in result
        assert result["metadata"]["research_type"] == "trend_analysis"
    
    def test_keyword_extraction(self):
        """Test keyword extraction from query."""
        agent = ResearchAgent({})
        query = "What are the environmental impacts of renewable energy technologies?"
        
        keywords = agent._extract_keywords(query)
        
        assert isinstance(keywords, list)
        assert len(keywords) > 0
        assert "environmental" in keywords
        assert "renewable" in keywords
        assert "energy" in keywords
        # Stop words should be filtered out
        assert "the" not in keywords
        assert "are" not in keywords
    
    def test_research_approach_determination(self):
        """Test research approach determination."""
        agent = ResearchAgent({})
        
        # Test quantitative approach
        query = "What are the market trends and statistics for AI adoption?"
        approach = agent._determine_research_approach(query, {})
        assert approach == "quantitative"
        
        # Test qualitative approach
        query = "What are people's opinions about remote work?"
        approach = agent._determine_research_approach(query, {})
        assert approach == "qualitative"
        
        # Test comparative approach
        query = "Compare Python vs JavaScript for web development"
        approach = agent._determine_research_approach(query, {})
        assert approach == "comparative"
        
        # Test historical approach
        query = "History of computer programming languages"
        approach = agent._determine_research_approach(query, {})
        assert approach == "historical"
    
    def test_research_plan_creation(self):
        """Test research plan creation."""
        agent = ResearchAgent({})
        query = "Impact of climate change on agriculture"
        context = {"depth": "comprehensive"}
        
        plan = agent._create_research_plan(query, context)
        
        assert "query" in plan
        assert "keywords" in plan
        assert "approach" in plan
        assert "search_strategies" in plan
        assert "estimated_sources" in plan
        assert "priority_areas" in plan
        assert isinstance(plan["search_strategies"], list)
        assert len(plan["search_strategies"]) > 0
    
    def test_information_gathering(self):
        """Test information gathering simulation."""
        agent = ResearchAgent({})
        query = "Machine learning applications"
        research_plan = {
            "search_strategies": [
                {"type": "primary", "keywords": ["machine", "learning"], "sources": ["academic", "news"]},
                {"type": "secondary", "keywords": ["applications"], "sources": ["reports"]}
            ]
        }
        max_sources = 5
        
        sources_data = agent._gather_information(query, research_plan, max_sources)
        
        assert isinstance(sources_data, list)
        assert len(sources_data) <= max_sources
        for source in sources_data:
            assert "id" in source
            assert "type" in source
            assert "title" in source
            assert "content" in source
            assert "credibility_score" in source
            assert "relevance_score" in source
    
    def test_information_analysis(self):
        """Test information analysis."""
        agent = ResearchAgent({})
        sources_data = [
            {
                "id": "source_1",
                "type": "academic",
                "content": "Sample academic content",
                "credibility_score": 0.9,
                "relevance_score": 0.8
            },
            {
                "id": "source_2", 
                "type": "news",
                "content": "Sample news content",
                "credibility_score": 0.7,
                "relevance_score": 0.9
            }
        ]
        query = "test query"
        
        analysis = agent._analyze_information(sources_data, query)
        
        assert "themes" in analysis
        assert "key_findings" in analysis
        assert "contradictions" in analysis
        assert "source_distribution" in analysis
        assert "credibility_analysis" in analysis
        assert isinstance(analysis["themes"], list)
        assert isinstance(analysis["key_findings"], list)
    
    def test_fact_verification(self):
        """Test fact verification."""
        agent = ResearchAgent({"fact_check_threshold": 0.8})
        analysis = {
            "key_findings": [
                {"finding": "Finding 1", "confidence": 0.9, "supporting_sources": ["s1", "s2"]},
                {"finding": "Finding 2", "confidence": 0.6, "supporting_sources": ["s3"]}
            ]
        }
        sources_data = []
        
        verification = agent._verify_facts(analysis, sources_data)
        
        assert "fact_checks" in verification
        assert "overall_confidence" in verification
        assert "verification_summary" in verification
        assert len(verification["fact_checks"]) == 2
        assert verification["fact_checks"][0]["verification_status"] == "verified"
        assert verification["fact_checks"][1]["verification_status"] == "needs_verification"
    
    def test_report_generation(self):
        """Test research report generation."""
        agent = ResearchAgent({})
        query = "Test research query"
        analysis = {
            "themes": [{"theme": "Main theme", "frequency": 0.8}],
            "key_findings": [{"finding": "Key finding 1", "confidence": 0.9}]
        }
        verification = {
            "overall_confidence": 0.85,
            "fact_checks": [{"finding_id": "finding_1", "confidence": 0.9}]
        }
        sources_data = [
            {"title": "Source 1", "author": "Author 1", "url": "http://example.com/1"}
        ]
        
        report = agent._generate_report(query, analysis, verification, sources_data)
        
        assert "summary" in report
        assert "findings" in report
        assert "sources" in report
        assert "citations" in report
        assert "methodology" in report
        assert query in report["summary"]
        assert isinstance(report["findings"], list)
        assert isinstance(report["citations"], list)


class TestAgentErrorHandling:
    """Test error handling across all agents."""
    
    def test_customer_support_agent_process_error(self):
        """Test error handling in customer support agent processing."""
        agent = CustomerSupportAgent({})
        
        # Mock an internal method to raise an exception
        with patch.object(agent, '_analyze_intent', side_effect=Exception("Test error")):
            with pytest.raises(AgentError) as exc_info:
                agent.process("test query", {})
            
            assert "Customer support processing failed" in str(exc_info.value)
            assert "Test error" in str(exc_info.value)
    
    def test_sales_agent_process_error(self):
        """Test error handling in sales agent processing."""
        agent = SalesAgent({})
        
        with patch.object(agent, '_analyze_sales_intent', side_effect=Exception("Test error")):
            with pytest.raises(AgentError) as exc_info:
                agent.process("test query", {})
            
            assert "Sales processing failed" in str(exc_info.value)
    
    def test_research_agent_process_error(self):
        """Test error handling in research agent processing."""
        agent = ResearchAgent({})
        
        with patch.object(agent, '_create_research_plan', side_effect=Exception("Test error")):
            with pytest.raises(AgentError) as exc_info:
                agent.process("test query", {})
            
            assert "Research processing failed" in str(exc_info.value)
    
    def test_agent_initialization_error(self):
        """Test error handling during agent initialization."""
        # Test with invalid configuration that causes setup to fail
        with patch('app.agents.customer_support.CustomerSupportAgent._setup_support_capabilities', 
                   side_effect=Exception("Setup failed")):
            with pytest.raises(AgentError) as exc_info:
                CustomerSupportAgent({})
            
            assert "Agent initialization failed" in str(exc_info.value)


class TestAgentHealthChecks:
    """Test health check functionality across all agents."""
    
    def test_customer_support_agent_health_check(self):
        """Test customer support agent health check."""
        agent = CustomerSupportAgent({})
        health = agent.health_check()
        
        assert health["status"] == "healthy"
        assert health["agent_id"] == agent.agent_id
        assert health["name"] == "customer_support"
        assert "uptime" in health
        assert "capabilities_count" in health
    
    def test_sales_agent_health_check(self):
        """Test sales agent health check."""
        agent = SalesAgent({})
        health = agent.health_check()
        
        assert health["status"] == "healthy"
        assert health["name"] == "sales"
    
    def test_research_agent_health_check(self):
        """Test research agent health check."""
        agent = ResearchAgent({})
        health = agent.health_check()
        
        assert health["status"] == "healthy"
        assert health["name"] == "research"
    
    def test_agent_health_check_error(self):
        """Test health check when agent has errors."""
        agent = CustomerSupportAgent({})
        
        # Simulate an error during health check
        with patch.object(agent, 'capabilities', side_effect=Exception("Health check error")):
            health = agent.health_check()
            
            assert health["status"] == "unhealthy"
            assert "error" in health
            assert health["agent_id"] == agent.agent_id
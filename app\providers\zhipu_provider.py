from __future__ import annotations
import os, json
from typing import Any
from .base import LLM
import logging
import requests

class ZhipuProvider(LLM):
    """
    Zhipu (GLM) provider using direct HTTP API.

    Args:
        model_env (str): Environment variable for model name.
        api_key_env (str): Environment variable for API key.
        timeout (float): Request timeout in seconds.

    Raises:
        RuntimeError: If API key is not set.

    Methods:
        generate: Generate a response from Zhipu GLM.
        parse_json: Parse JSON from model output.
    """
    API_URL = "https://api.z.ai/api/paas/v4/chat/completions"

    def __init__(self, model_env: str = "ZHIPUAI_MODEL", api_key_env: str = "ZHIPUAI_API_KEY", timeout: float = 30.0):
        self.api_key = os.getenv(api_key_env)
        if not self.api_key:
            raise RuntimeError(f"{api_key_env} is not set")
        self.model = os.getenv(model_env, "glm-4.5-flash")
        self.timeout = timeout

    def generate(self, prompt: str) -> str:
        """
        Generate a response from Zhipu GLM.

        Args:
            prompt (str): User prompt.

        Returns:
            str: Model response.

        Raises:
            requests.RequestException: On network/API error.
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "accept-language": "en-US,en",
            "content-type": "application/json"
        }
        payload = {
            "model": self.model,
            "messages": [
                {"role": "system", "content": "You are a helpful AI assistant."},
                {"role": "user", "content": prompt},
            ],
            "temperature": 0.2,
        }
        try:
            r = requests.post(self.API_URL, json=payload, headers=headers, timeout=self.timeout)
            r.raise_for_status()
            return r.json()["choices"][0]["message"]["content"]
        except requests.RequestException as e:
            logging.error(f"ZhipuAI API connection error: {e}")
            return ""

    def parse_json(self, text: str) -> Any:
        """
        Parse JSON from model output.

        Args:
            text (str): Text to parse.

        Returns:
            Any: Parsed JSON object.

        Raises:
            Exception: If parsing fails.
        """
        try:
            return json.loads(text)
        except Exception:
            start, end = text.find("{"), text.rfind("}")
            if start != -1 and end != -1 and end > start:
                return json.loads(text[start:end+1])
            raise

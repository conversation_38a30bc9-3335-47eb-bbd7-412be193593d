from __future__ import annotations

from typing import Any, List, Dict, Optional
from pydantic import BaseModel
import logging
import time
from ..core.types import Message

logger = logging.getLogger(__name__)

# --- Concrete Memory Types ---
class ShortTermMemory:
    """
    Short-term memory for an agent session.
    Maintains a token-capped window of recent messages, optionally with summary.

    Args:
        agent_id (str): Agent identifier.
        user_id (str): User identifier.
        session_id (str): Session identifier.
        max_tokens (int): Maximum tokens to keep in memory.

    Methods:
        add_message: Add a message to memory.
        get_messages: Retrieve recent messages.
        clear: Clear all messages.
    """
    def __init__(self, agent_id: str, user_id: str, session_id: str, adapter=None, max_tokens: int = 2048):
        """
        Initialize short-term memory.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            session_id: Session identifier
            adapter: Storage adapter (if None, uses in-memory list)
            max_tokens: Maximum tokens to keep in memory
        """
        self.agent_id = agent_id
        self.user_id = user_id
        self.session_id = session_id
        self.max_tokens = max_tokens
        self.adapter = adapter

        # Fallback to in-memory storage if no adapter provided
        if self.adapter is None:
            self.messages: List[Message] = []
            logger.debug(f"ShortTermMemory initialized without adapter for {agent_id}:{user_id}:{session_id}")
        else:
            logger.debug(f"ShortTermMemory initialized with adapter for {agent_id}:{user_id}:{session_id}")

    def add_message(self, agent_id: str, user_id: str, session_id: str, message: Message) -> None:
        """
        Add a message to memory.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            session_id: Session identifier
            message: Message to add
        """
        try:
            if self.adapter:
                self.adapter.add_message(agent_id, user_id, session_id, message)
            else:
                self.messages.append(message)
                # Implement token limiting for in-memory storage
                # TODO: Add token counting and trimming logic

            logger.debug(f"Added message to short-term memory: {agent_id}:{user_id}:{session_id}")
        except Exception as e:
            logger.error(f"Failed to add message to short-term memory: {e}")
            raise

    def get_messages(self, agent_id: str, user_id: str, session_id: str, limit: Optional[int] = None) -> List[Message]:
        """
        Retrieve recent messages.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            session_id: Session identifier
            limit: Maximum number of messages to retrieve

        Returns:
            List of messages
        """
        try:
            if self.adapter:
                return self.adapter.get_messages(agent_id, user_id, session_id, limit)
            else:
                messages = self.messages[-limit:] if limit else self.messages
                logger.debug(f"Retrieved {len(messages)} messages from short-term memory: {agent_id}:{user_id}:{session_id}")
                return messages
        except Exception as e:
            logger.error(f"Failed to retrieve messages from short-term memory: {e}")
            return []

    def clear(self, agent_id: str, user_id: str, session_id: Optional[str] = None) -> None:
        """
        Clear all messages.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            session_id: Session identifier
        """
        try:
            if self.adapter:
                key = f"{agent_id}:{user_id}:{session_id or 'default'}"
                self.adapter.clear(key)
            else:
                self.messages.clear()

            logger.debug(f"Cleared short-term memory: {agent_id}:{user_id}:{session_id}")
        except Exception as e:
            logger.error(f"Failed to clear short-term memory: {e}")
            raise

class EpisodicMemory:
    """
    Episodic memory for storing sequences of events/interactions.

    Args:
        agent_id (str): Agent identifier.
        user_id (str): User identifier.
        session_id (str): Session identifier.
        adapter: Storage adapter for persistence.

    Methods:
        log_event: Add an event to episodic memory.
        get_episodes: Retrieve episodes.
        clear: Clear all episodes.
    """

    def __init__(self, agent_id: str, user_id: str, session_id: str, adapter=None):
        """
        Initialize episodic memory.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            session_id: Session identifier
            adapter: Storage adapter (if None, uses in-memory list)
        """
        self.agent_id = agent_id
        self.user_id = user_id
        self.session_id = session_id
        self.adapter = adapter

        # Fallback to in-memory storage if no adapter provided
        if self.adapter is None:
            self.episodes: List[Dict[str, Any]] = []
            logger.debug(f"EpisodicMemory initialized without adapter for {agent_id}:{user_id}:{session_id}")
        else:
            logger.debug(f"EpisodicMemory initialized with adapter for {agent_id}:{user_id}:{session_id}")

    def log_event(self, event: Dict[str, Any]) -> None:
        """
        Add an event to episodic memory.

        Args:
            event: Event data to log
        """
        try:
            # Ensure event has required fields
            event_data = {
                **event,
                "agent_id": self.agent_id,
                "user_id": self.user_id,
                "session_id": self.session_id,
                "timestamp": event.get("timestamp", time.time())
            }

            if self.adapter:
                self.adapter.log_event(event_data)
            else:
                self.episodes.append(event_data)

            logger.debug(f"Logged event to episodic memory: {self.agent_id}:{self.user_id}:{self.session_id}")
        except Exception as e:
            logger.error(f"Failed to log event to episodic memory: {e}")
            raise

    def get_episodes(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Retrieve episodes.

        Args:
            limit: Maximum number of episodes to retrieve

        Returns:
            List of episodes
        """
        try:
            if self.adapter:
                key = f"{self.agent_id}:{self.user_id}:{self.session_id}:episodic"
                items = self.adapter.get(key, limit)
                logger.debug(f"Retrieved {len(items)} episodes from episodic memory: {self.agent_id}:{self.user_id}:{self.session_id}")
                return items
            else:
                episodes = self.episodes[-limit:] if limit else self.episodes
                logger.debug(f"Retrieved {len(episodes)} episodes from episodic memory: {self.agent_id}:{self.user_id}:{self.session_id}")
                return episodes
        except Exception as e:
            logger.error(f"Failed to retrieve episodes from episodic memory: {e}")
            return []

    def clear(self) -> None:
        """
        Clear all episodes.
        """
        try:
            if self.adapter:
                key = f"{self.agent_id}:{self.user_id}:{self.session_id}:episodic"
                self.adapter.clear(key)
            else:
                self.episodes.clear()

            logger.debug(f"Cleared episodic memory: {self.agent_id}:{self.user_id}:{self.session_id}")
        except Exception as e:
            logger.error(f"Failed to clear episodic memory: {e}")
            raise

class ProceduralMemory:
    """
    Procedural memory for storing learned procedures, workflows, or action sequences.

    Args:
        agent_id (str): Agent identifier.
        user_id (str): User identifier.
        adapter: Storage adapter for persistence.

    Methods:
        add_procedure: Add a procedure.
        get_procedure: Retrieve a procedure by name.
        list_procedures: List all procedure names.
        clear: Clear all procedures.
    """

    def __init__(self, agent_id: str, user_id: str, adapter=None):
        """
        Initialize procedural memory.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            adapter: Storage adapter (if None, uses in-memory dict)
        """
        self.agent_id = agent_id
        self.user_id = user_id
        self.adapter = adapter

        # Fallback to in-memory storage if no adapter provided
        if self.adapter is None:
            self.procedures: Dict[str, Dict[str, Any]] = {}
            logger.debug(f"ProceduralMemory initialized without adapter for {agent_id}:{user_id}")
        else:
            logger.debug(f"ProceduralMemory initialized with adapter for {agent_id}:{user_id}")

    def add_procedure(self, name: str, procedure: Dict[str, Any]) -> None:
        """
        Add a procedure.

        Args:
            name: Procedure name
            procedure: Procedure data
        """
        try:
            procedure_data = {
                **procedure,
                "name": name,
                "agent_id": self.agent_id,
                "user_id": self.user_id,
                "timestamp": time.time()
            }

            if self.adapter:
                self.adapter.add_procedure(self.agent_id, self.user_id, procedure_data)
            else:
                self.procedures[name] = procedure_data

            logger.debug(f"Added procedure '{name}' to procedural memory: {self.agent_id}:{self.user_id}")
        except Exception as e:
            logger.error(f"Failed to add procedure to procedural memory: {e}")
            raise

    def get_procedure(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a procedure by name.

        Args:
            name: Procedure name

        Returns:
            Procedure data or None if not found
        """
        try:
            if self.adapter:
                key = f"{self.agent_id}:{self.user_id}:procedural"
                items = self.adapter.get(key)
                for item in items:
                    if item.get("name") == name:
                        return item
                return None
            else:
                return self.procedures.get(name)
        except Exception as e:
            logger.error(f"Failed to retrieve procedure from procedural memory: {e}")
            return None

    def list_procedures(self) -> List[str]:
        """
        List all procedure names.

        Returns:
            List of procedure names
        """
        try:
            if self.adapter:
                key = f"{self.agent_id}:{self.user_id}:procedural"
                items = self.adapter.get(key)
                names = [item.get("name") for item in items if item.get("name")]
                return names
            else:
                return list(self.procedures.keys())
        except Exception as e:
            logger.error(f"Failed to list procedures from procedural memory: {e}")
            return []

    def clear(self) -> None:
        """
        Clear all procedures.
        """
        try:
            if self.adapter:
                key = f"{self.agent_id}:{self.user_id}:procedural"
                self.adapter.clear(key)
            else:
                self.procedures.clear()

            logger.debug(f"Cleared procedural memory: {self.agent_id}:{self.user_id}")
        except Exception as e:
            logger.error(f"Failed to clear procedural memory: {e}")
            raise

class SemanticMemory:
    """
    Semantic memory for storing facts, concepts, relationships (knowledge graph, embeddings).

    Args:
        agent_id (str): Agent identifier.
        user_id (str): User identifier.
        adapter: Storage adapter for persistence (typically VectorDB for semantic search).

    Methods:
        add_fact: Add a fact or concept.
        search: Search for facts/concepts.
        clear: Clear all facts.
    """

    def __init__(self, agent_id: str, user_id: str, adapter=None):
        """
        Initialize semantic memory.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            adapter: Storage adapter (if None, uses in-memory list)
        """
        self.agent_id = agent_id
        self.user_id = user_id
        self.adapter = adapter

        # Fallback to in-memory storage if no adapter provided
        if self.adapter is None:
            self.facts: List[Dict[str, Any]] = []
            logger.debug(f"SemanticMemory initialized without adapter for {agent_id}:{user_id}")
        else:
            logger.debug(f"SemanticMemory initialized with adapter for {agent_id}:{user_id}")

    def add_fact(self, fact: Dict[str, Any]) -> None:
        """
        Add a fact or concept.

        Args:
            fact: Fact data to store
        """
        try:
            fact_data = {
                **fact,
                "agent_id": self.agent_id,
                "user_id": self.user_id,
                "timestamp": time.time()
            }

            if self.adapter:
                self.adapter.add_fact(self.agent_id, self.user_id, fact_data)
            else:
                self.facts.append(fact_data)

            logger.debug(f"Added fact to semantic memory: {self.agent_id}:{self.user_id}")
        except Exception as e:
            logger.error(f"Failed to add fact to semantic memory: {e}")
            raise

    def search(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Search for facts/concepts.

        Args:
            query: Search query
            limit: Maximum number of results

        Returns:
            List of matching facts
        """
        try:
            if self.adapter:
                # If adapter supports semantic search (like VectorDB), use it
                if hasattr(self.adapter, 'search') and callable(getattr(self.adapter, 'search')):
                    # For vector search, we need embeddings
                    # This is a simplified implementation - in practice, you'd generate embeddings for the query
                    logger.warning("Semantic search requires query embeddings - returning empty results")
                    return []
                else:
                    # Fallback to getting recent facts
                    key = f"{self.agent_id}:{self.user_id}:semantic"
                    items = self.adapter.get(key, limit)
                    return items
            else:
                # Simple text-based search in memory (very basic)
                matching_facts = []
                query_lower = query.lower()

                for fact in self.facts:
                    fact_text = str(fact).lower()
                    if query_lower in fact_text:
                        matching_facts.append(fact)

                # Return most recent matches up to limit
                return matching_facts[-limit:] if matching_facts else self.facts[-limit:]

        except Exception as e:
            logger.error(f"Failed to search semantic memory: {e}")
            return []

    def clear(self) -> None:
        """
        Clear all facts.
        """
        try:
            if self.adapter:
                key = f"{self.agent_id}:{self.user_id}:semantic"
                self.adapter.clear(key)
            else:
                self.facts.clear()

            logger.debug(f"Cleared semantic memory: {self.agent_id}:{self.user_id}")
        except Exception as e:
            logger.error(f"Failed to clear semantic memory: {e}")
            raise

class AgentMemory(BaseModel):
    """
    Per-agent memory, partitioned by user/session, with submodules for each memory type.

    Args:
        agent_id (str): Agent identifier.
        user_id (str): User identifier.
        session_id (str, optional): Session identifier.
        short_term, episodic, procedural, semantic: Memory type instances.

    Raises:
        None
    """

    agent_id: str
    user_id: str
    session_id: Optional[str] = None
    short_term: Optional[ShortTermMemory] = None
    episodic: Optional[EpisodicMemory] = None
    procedural: Optional[ProceduralMemory] = None
    semantic: Optional[SemanticMemory] = None

    class Config:
        arbitrary_types_allowed = True

    def __init__(self, adapters: Optional[Dict[str, Any]] = None, **data):
        """
        Initialize agent memory with optional adapters.

        Args:
            adapters: Dictionary of memory type to adapter mappings
                     e.g., {"short_term": adapter1, "episodic": adapter2, ...}
            **data: Additional initialization data
        """
        super().__init__(**data)

        adapters = adapters or {}

        # Initialize all memory types if not provided
        if self.short_term is None:
            self.short_term = ShortTermMemory(
                self.agent_id,
                self.user_id,
                self.session_id or "default",
                adapter=adapters.get("short_term")
            )
        if self.episodic is None:
            self.episodic = EpisodicMemory(
                self.agent_id,
                self.user_id,
                self.session_id or "default",
                adapter=adapters.get("episodic")
            )
        if self.procedural is None:
            self.procedural = ProceduralMemory(
                self.agent_id,
                self.user_id,
                adapter=adapters.get("procedural")
            )
        if self.semantic is None:
            self.semantic = SemanticMemory(
                self.agent_id,
                self.user_id,
                adapter=adapters.get("semantic")
            )

class MemoryManager:
    """
    Central class to manage all memory types, route queries, and handle persistence.

    This class provides a unified interface for managing agent memory across different
    storage backends and memory types.

    Methods:
        get_agent_memory: Retrieve or create AgentMemory for agent/user/session.
        add_short_term_message: Add message to short-term memory.
        log_episode: Add event to episodic memory.
        add_procedure: Add procedure to procedural memory.
        add_fact: Add fact to semantic memory.
        search_semantic: Search semantic memory.
        clear_memory: Clear specific memory types.
    """

    def __init__(self, adapters: Optional[Dict[str, Any]] = None):
        """
        Initialize memory manager with optional adapters.

        Args:
            adapters: Dictionary of memory type to adapter mappings
                     e.g., {"short_term": adapter1, "episodic": adapter2, ...}
        """
        self.agent_memories: Dict[str, AgentMemory] = {}
        self.adapters = adapters or {}
        logger.info(f"MemoryManager initialized with adapters: {list(self.adapters.keys())}")

    def get_agent_memory(self, agent_id: str, user_id: str, session_id: Optional[str] = None) -> AgentMemory:
        """
        Retrieve or create AgentMemory for agent/user/session.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            session_id: Session identifier

        Returns:
            AgentMemory instance
        """
        key = f"{agent_id}:{user_id}:{session_id or ''}"
        if key not in self.agent_memories:
            self.agent_memories[key] = AgentMemory(
                agent_id=agent_id,
                user_id=user_id,
                session_id=session_id,
                adapters=self.adapters
            )
            logger.debug(f"Created new AgentMemory: {key}")
        return self.agent_memories[key]

    # Memory operation routing methods
    def add_short_term_message(self, agent_id: str, user_id: str, session_id: str, message: Message) -> None:
        """
        Add message to short-term memory.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            session_id: Session identifier
            message: Message to add
        """
        try:
            mem = self.get_agent_memory(agent_id, user_id, session_id)
            mem.short_term.add_message(agent_id, user_id, session_id, message)
            logger.debug(f"Added message to short-term memory: {agent_id}:{user_id}:{session_id}")
        except Exception as e:
            logger.error(f"Failed to add short-term message: {e}")
            raise

    def log_episode(self, agent_id: str, user_id: str, session_id: str, event: Dict[str, Any]) -> None:
        """
        Add event to episodic memory.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            session_id: Session identifier
            event: Event data to log
        """
        try:
            mem = self.get_agent_memory(agent_id, user_id, session_id)
            mem.episodic.log_event(event)
            logger.debug(f"Logged episode: {agent_id}:{user_id}:{session_id}")
        except Exception as e:
            logger.error(f"Failed to log episode: {e}")
            raise

    def add_procedure(self, agent_id: str, user_id: str, name: str, procedure: Dict[str, Any]) -> None:
        """
        Add procedure to procedural memory.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            name: Procedure name
            procedure: Procedure data
        """
        try:
            mem = self.get_agent_memory(agent_id, user_id)
            mem.procedural.add_procedure(name, procedure)
            logger.debug(f"Added procedure '{name}': {agent_id}:{user_id}")
        except Exception as e:
            logger.error(f"Failed to add procedure: {e}")
            raise

    def add_fact(self, agent_id: str, user_id: str, fact: Dict[str, Any]) -> None:
        """
        Add fact to semantic memory.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            fact: Fact data to add
        """
        try:
            mem = self.get_agent_memory(agent_id, user_id)
            mem.semantic.add_fact(fact)
            logger.debug(f"Added fact: {agent_id}:{user_id}")
        except Exception as e:
            logger.error(f"Failed to add fact: {e}")
            raise

    def search_semantic(self, agent_id: str, user_id: str, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Search semantic memory.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            query: Search query
            limit: Maximum number of results

        Returns:
            List of matching facts
        """
        try:
            mem = self.get_agent_memory(agent_id, user_id)
            results = mem.semantic.search(query, limit)
            logger.debug(f"Searched semantic memory: {agent_id}:{user_id}, found {len(results)} results")
            return results
        except Exception as e:
            logger.error(f"Failed to search semantic memory: {e}")
            return []

    def get_short_term_messages(self, agent_id: str, user_id: str, session_id: str, limit: Optional[int] = None) -> List[Message]:
        """
        Get messages from short-term memory.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            session_id: Session identifier
            limit: Maximum number of messages to retrieve

        Returns:
            List of messages
        """
        try:
            mem = self.get_agent_memory(agent_id, user_id, session_id)
            messages = mem.short_term.get_messages(agent_id, user_id, session_id, limit)
            logger.debug(f"Retrieved {len(messages)} short-term messages: {agent_id}:{user_id}:{session_id}")
            return messages
        except Exception as e:
            logger.error(f"Failed to get short-term messages: {e}")
            return []

    def clear_memory(self, agent_id: str, user_id: str, session_id: Optional[str] = None, memory_types: Optional[List[str]] = None) -> None:
        """
        Clear specific memory types.

        Args:
            agent_id: Agent identifier
            user_id: User identifier
            session_id: Session identifier
            memory_types: List of memory types to clear (default: all)
        """
        try:
            mem = self.get_agent_memory(agent_id, user_id, session_id)
            memory_types = memory_types or ["short_term", "episodic", "procedural", "semantic"]

            if "short_term" in memory_types:
                mem.short_term.clear(agent_id, user_id, session_id or "default")
            if "episodic" in memory_types:
                mem.episodic.clear()
            if "procedural" in memory_types:
                mem.procedural.clear()
            if "semantic" in memory_types:
                mem.semantic.clear()

            logger.info(f"Cleared memory types {memory_types}: {agent_id}:{user_id}:{session_id}")
        except Exception as e:
            logger.error(f"Failed to clear memory: {e}")
            raise

# Enhanced Customer Support Agent Configuration
# This configuration demonstrates the new features including personas, system prompts,
# LLM provider settings, and tool integrations

# Agent Identity and Behavior
persona: |
  You are <PERSON>, a friendly and professional customer support specialist with 5+ years of experience. 
  You are empathetic, patient, and solution-oriented. You have deep knowledge of products and services, 
  and you're skilled at de-escalating tense situations. You always maintain a positive attitude and 
  strive to exceed customer expectations.

system_prompt: |
  You are a professional customer support agent. Your role is to:

  1. **Listen actively** - Understand the customer's issue completely before responding
  2. **Show empathy** - Acknowledge the customer's frustration and validate their concerns
  3. **Provide solutions** - Offer clear, actionable steps to resolve their issue
  4. **Be proactive** - Anticipate follow-up questions and provide comprehensive answers
  5. **Escalate when needed** - Know when to involve supervisors or specialists

  **Guidelines:**
  - Always greet customers warmly and professionally
  - Use clear, jargon-free language
  - Provide step-by-step instructions when applicable
  - Offer alternatives if the primary solution isn't suitable
  - Follow up to ensure satisfaction
  - Document interactions for future reference

  **Tone:** Professional, friendly, helpful, and patient
  **Goal:** Resolve customer issues efficiently while maintaining high satisfaction

# LLM Provider Configuration
llm_provider: "ollama"
llm_model: "gemma3:270m"

# Tools Configuration
tools:
  - "http_get"        # For API calls and web requests
  - "searxng_search"  # For web search capabilities

# Memory Configuration
memory:
  short_term:
    type: "short_term"
    adapter: "in_memory"  # Fast, ephemeral
    max_size: 1000
    session_based: true
    max_sessions: 100
    max_messages_per_session: 50
  episodic:
    type: "episodic"
    adapter: "sqlite"  # Persistent, local
    sqlite_path: "./data/customer_support_episodic.sqlite"
    max_size: 5000
  procedural:
    type: "procedural"
    adapter: "jsonl"  # File-based
    jsonl_path: "./data/customer_support_procedural.jsonl"
    max_size: 2000
  semantic:
    type: "semantic"
    adapter: "vectordb" # Qdrant
    url: "http://localhost:6333"
    collection: "customer_support_semantic"

# Planner Configuration
planner:
  type: "react"
  max_steps: 10
  temperature: 0.7

# Customer Support Specific Settings
escalation_threshold: 0.3
max_escalation_level: 3
max_resolution_attempts: 3
auto_escalate_keywords:
  - "urgent"
  - "critical"
  - "emergency"
  - "lawsuit"
  - "legal"
  - "complaint"
  - "manager"
  - "supervisor"

# Language Support
supported_languages:
  - "en"
  - "es"
  - "fr"

# Agent Capabilities
capabilities:
  - "ticket_management"
  - "knowledge_base_search"
  - "issue_classification"
  - "escalation_handling"
  - "customer_interaction"

# Custom Settings
custom_settings:
  priority_queue: true
  auto_escalate: false
  sentiment_analysis: true
  satisfaction_tracking: true
  response_templates: true

# Response Templates
response_templates:
  greeting: "Hello! I'm Alex, your customer support specialist. How can I help you today?"
  acknowledgment: "I understand your concern and I'm here to help resolve this issue."
  escalation: "I'm going to connect you with a specialist who can better assist with this matter."
  closing: "Is there anything else I can help you with today?"

# Integration Settings
integrations:
  knowledge_base:
    enabled: true
    search_threshold: 0.8
  
  crm:
    enabled: true
    auto_update: true
  
  ticketing_system:
    enabled: true
    auto_create: true
    priority_mapping:
      low: 1
      medium: 2
      high: 3
      critical: 4

# Monitoring and Analytics
monitoring:
  track_response_time: true
  track_resolution_rate: true
  track_customer_satisfaction: true
  log_interactions: true

# Quality Assurance
quality_assurance:
  review_threshold: 0.7
  auto_review: true
  feedback_collection: true

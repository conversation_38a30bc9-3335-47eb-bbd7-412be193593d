import httpx
from .base import ToolS<PERSON>, Tool

def _http_get(params):
    url = params["url"]
    timeout = float(params.get("timeout", 10))
    r = httpx.get(url, timeout=timeout)
    r.raise_for_status()
    # keep it compact to avoid flooding context
    return {"status": r.status_code, "headers": dict(r.headers), "text": r.text[:4000]}

http_get = Tool(
    ToolSpec(
        name="http.get",
        description="Fetch a URL over HTTP GET",
        input_schema={"type": "object", "properties": {"url": {"type": "string"}, "timeout": {"type": "number"}}, "required": ["url"]},
        output_schema={"type": "object"},
    ),
    _http_get,
)

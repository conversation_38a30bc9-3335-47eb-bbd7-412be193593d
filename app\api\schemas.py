from pydantic import BaseModel
from typing import Optional, List, Dict, Any, Union

class RunRequest(BaseModel):
    goal: str

class RunResponse(BaseModel):
    finish: bool
    result: str | None = None

class MultiModalMessage(BaseModel):
    """Message that can contain text, images, or other media types."""
    role: str
    content: Union[str, List[Dict[str, Any]]]
    media_type: Optional[str] = "text"
    metadata: Optional[Dict[str, Any]] = None

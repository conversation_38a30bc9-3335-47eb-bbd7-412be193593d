"""
Integration tests for memory system with agent API endpoints.

This module contains comprehensive integration tests for the memory system
integrated with the agent factory API, testing the complete memory lifecycle.
"""

import pytest
import json
import time
from unittest.mock import patch, Mock
from fastapi.testclient import TestClient

from app.api.main import app
from app.core.types import Message


@pytest.fixture
def client():
    """Create test client for API testing."""
    return TestClient(app)


class TestMemoryIntegrationAPI:
    """Test memory system integration with agent API."""

    def test_create_agent_with_memory_system(self, client):
        """Test creating agent with comprehensive memory configuration."""
        agent_data = {
            "agent_type": "customer_support",
            "name": "MemoryIntegrationAgent",
            "config": {
                "memory": {
                    "short_term": {"adapter": "in_memory"},
                    "episodic": {"adapter": "in_memory"},
                    "procedural": {"adapter": "in_memory"},
                    "semantic": {"adapter": "in_memory"}
                },
                "tools": [],
                "planner": {}
            }
        }

        response = client.post("/agent-factory/agents/create", json=agent_data)
        
        assert response.status_code == 200
        response_data = response.json()
        
        assert "agent_id" in response_data
        assert response_data["name"] == "MemoryIntegrationAgent"
        assert response_data["status"] == "created"
        
        return response_data["agent_id"]

    def test_chat_with_memory_persistence(self, client):
        """Test that chat messages are stored and retrieved from memory."""
        # Create agent
        agent_data = {
            "agent_type": "customer_support",
            "name": "ChatMemoryAgent",
            "config": {
                "memory": {
                    "short_term": {"adapter": "in_memory"},
                    "episodic": {"adapter": "in_memory"},
                    "procedural": {"adapter": "in_memory"},
                    "semantic": {"adapter": "in_memory"}
                }
            }
        }

        create_response = client.post("/agent-factory/agents/create", json=agent_data)
        assert create_response.status_code == 200
        agent_id = create_response.json()["agent_id"]

        # First chat message
        chat_data_1 = {
            "message": "Hello, my name is Alice and I work at TechCorp",
            "session_id": "memory_persistence_session",
            "context": {
                "user_id": "alice_user",
                "timestamp": time.time()
            }
        }

        response_1 = client.post(f"/agent-factory/agents/{agent_id}/chat", json=chat_data_1)
        assert response_1.status_code == 200
        
        # Second chat message referencing first
        chat_data_2 = {
            "message": "What company do I work for?",
            "session_id": "memory_persistence_session",
            "context": {
                "user_id": "alice_user",
                "timestamp": time.time()
            }
        }

        response_2 = client.post(f"/agent-factory/agents/{agent_id}/chat", json=chat_data_2)
        assert response_2.status_code == 200
        
        response_data_2 = response_2.json()
        
        # Check that conversation history is maintained
        assert "metadata" in response_data_2
        assert "conversation_length" in response_data_2["metadata"]
        assert response_data_2["metadata"]["conversation_length"] >= 2

    def test_episodic_memory_logging(self, client):
        """Test that episodic memory logs events during chat."""
        # Create agent
        agent_data = {
            "agent_type": "research_assistant",
            "name": "EpisodicMemoryAgent",
            "config": {
                "memory": {
                    "short_term": {"adapter": "in_memory"},
                    "episodic": {"adapter": "in_memory"},
                    "procedural": {"adapter": "in_memory"},
                    "semantic": {"adapter": "in_memory"}
                }
            }
        }

        create_response = client.post("/agent-factory/agents/create", json=agent_data)
        assert create_response.status_code == 200
        agent_id = create_response.json()["agent_id"]

        # Chat with context that should be logged episodically
        chat_data = {
            "message": "I need help with a complex research task on AI trends",
            "session_id": "episodic_logging_session",
            "context": {
                "user_id": "researcher_user",
                "task_type": "research",
                "priority": "high",
                "domain": "artificial_intelligence"
            }
        }

        response = client.post(f"/agent-factory/agents/{agent_id}/chat", json=chat_data)
        assert response.status_code == 200
        
        # Episodic memory logging should happen automatically
        response_data = response.json()
        assert "response" in response_data
        assert "metadata" in response_data

    def test_multi_session_memory_isolation(self, client):
        """Test that different sessions maintain separate short-term memory."""
        # Create agent
        agent_data = {
            "agent_type": "customer_support",
            "name": "MultiSessionAgent",
            "config": {
                "memory": {
                    "short_term": {"adapter": "in_memory"},
                    "episodic": {"adapter": "in_memory"},
                    "procedural": {"adapter": "in_memory"},
                    "semantic": {"adapter": "in_memory"}
                }
            }
        }

        create_response = client.post("/agent-factory/agents/create", json=agent_data)
        assert create_response.status_code == 200
        agent_id = create_response.json()["agent_id"]

        user_id = "multi_session_user"

        # Chat in session 1
        chat_data_session1 = {
            "message": "My favorite color is blue",
            "session_id": "session_1",
            "context": {"user_id": user_id}
        }

        response_1 = client.post(f"/agent-factory/agents/{agent_id}/chat", json=chat_data_session1)
        assert response_1.status_code == 200

        # Chat in session 2
        chat_data_session2 = {
            "message": "My favorite color is red",
            "session_id": "session_2",
            "context": {"user_id": user_id}
        }

        response_2 = client.post(f"/agent-factory/agents/{agent_id}/chat", json=chat_data_session2)
        assert response_2.status_code == 200

        # Each session should have separate conversation history
        assert response_1.json()["metadata"]["session_id"] == "session_1"
        assert response_2.json()["metadata"]["session_id"] == "session_2"

    @patch('app.memory.adapters_vectordb.VectorDBAdapter')
    def test_create_agent_with_vectordb_memory(self, mock_vectordb, client):
        """Test agent creation with VectorDB memory adapter."""
        # Mock VectorDB to avoid requiring actual Qdrant server
        mock_adapter = Mock()
        mock_vectordb.return_value = mock_adapter
        
        agent_data = {
            "agent_type": "research_assistant",
            "name": "VectorDBAgent",
            "config": {
                "memory": {
                    "short_term": {"adapter": "in_memory"},
                    "episodic": {"adapter": "in_memory"},
                    "procedural": {"adapter": "in_memory"},
                    "semantic": {
                        "adapter": "vectordb",
                        "url": "http://localhost:6333",
                        "collection": "test_collection"
                    }
                }
            }
        }

        response = client.post("/agent-factory/agents/create", json=agent_data)
        assert response.status_code == 200
        
        response_data = response.json()
        assert "agent_id" in response_data
        assert response_data["name"] == "VectorDBAgent"
        
        # VectorDB adapter should have been instantiated
        mock_vectordb.assert_called_once()

    def test_memory_error_resilience(self, client):
        """Test that agent continues to work even with memory errors."""
        # Create agent with basic memory configuration
        agent_data = {
            "agent_type": "customer_support",
            "name": "ResilienceTestAgent",
            "config": {
                "memory": {
                    "short_term": {"adapter": "in_memory"},
                    "episodic": {"adapter": "in_memory"},
                    "procedural": {"adapter": "in_memory"},
                    "semantic": {"adapter": "in_memory"}
                }
            }
        }

        create_response = client.post("/agent-factory/agents/create", json=agent_data)
        assert create_response.status_code == 200
        agent_id = create_response.json()["agent_id"]

        # Chat should work even if memory operations encounter issues
        chat_data = {
            "message": "Simple test message for resilience",
            "session_id": "resilience_test_session",
            "context": {"user_id": "resilience_test_user"}
        }

        response = client.post(f"/agent-factory/agents/{agent_id}/chat", json=chat_data)
        assert response.status_code == 200
        
        response_data = response.json()
        assert "response" in response_data
        assert "agent_id" in response_data

    def test_mixed_memory_adapters(self, client):
        """Test agent with different memory adapters for each memory type."""
        agent_data = {
            "agent_type": "research_assistant",
            "name": "MixedMemoryAgent",
            "config": {
                "memory": {
                    "short_term": {"adapter": "in_memory"},
                    "episodic": {"adapter": "jsonl", "jsonl_path": "./test_episodic.jsonl"},
                    "procedural": {"adapter": "in_memory"},
                    "semantic": {"adapter": "in_memory"}  # Would be vectordb in production
                }
            }
        }

        response = client.post("/agent-factory/agents/create", json=agent_data)
        assert response.status_code == 200
        
        response_data = response.json()
        assert "agent_id" in response_data
        assert response_data["name"] == "MixedMemoryAgent"

        # Test chat with mixed memory adapters
        agent_id = response_data["agent_id"]
        chat_data = {
            "message": "Test message with mixed memory adapters",
            "session_id": "mixed_memory_session",
            "context": {"user_id": "mixed_memory_user"}
        }

        chat_response = client.post(f"/agent-factory/agents/{agent_id}/chat", json=chat_data)
        assert chat_response.status_code == 200

    def test_memory_system_comprehensive_workflow(self, client):
        """Test comprehensive workflow involving all memory types."""
        # Create agent
        agent_data = {
            "agent_type": "research_assistant",
            "name": "ComprehensiveWorkflowAgent",
            "config": {
                "memory": {
                    "short_term": {"adapter": "in_memory"},
                    "episodic": {"adapter": "in_memory"},
                    "procedural": {"adapter": "in_memory"},
                    "semantic": {"adapter": "in_memory"}
                }
            }
        }

        create_response = client.post("/agent-factory/agents/create", json=agent_data)
        assert create_response.status_code == 200
        agent_id = create_response.json()["agent_id"]

        # Simulate a complex interaction that should engage all memory types
        chat_data = {
            "message": "I need help learning a new data analysis procedure. Can you teach me step by step and remember my progress?",
            "session_id": "comprehensive_workflow_session",
            "context": {
                "user_id": "learning_user",
                "task_type": "learning",
                "domain": "data_analysis",
                "skill_level": "beginner"
            }
        }

        response = client.post(f"/agent-factory/agents/{agent_id}/chat", json=chat_data)
        assert response.status_code == 200
        
        response_data = response.json()
        assert "response" in response_data
        assert "metadata" in response_data
        
        # This interaction should:
        # - Store conversation in short-term memory
        # - Log the learning request in episodic memory
        # - Potentially store/retrieve procedures in procedural memory
        # - Store/retrieve facts about data analysis in semantic memory

    def test_agent_memory_initialization_error_handling(self, client):
        """Test error handling during agent memory initialization."""
        # Test with invalid memory adapter configuration
        agent_data = {
            "agent_type": "customer_support",
            "name": "ErrorHandlingAgent",
            "config": {
                "memory": {
                    "short_term": {"adapter": "invalid_adapter"},  # Invalid adapter
                    "episodic": {"adapter": "in_memory"},
                    "procedural": {"adapter": "in_memory"},
                    "semantic": {"adapter": "in_memory"}
                }
            }
        }

        response = client.post("/agent-factory/agents/create", json=agent_data)
        
        # Should handle gracefully - either create with fallback or return appropriate error
        assert response.status_code in [200, 400, 422]
        
        if response.status_code == 200:
            # If agent was created successfully (with fallback), test that it works
            agent_id = response.json()["agent_id"]
            
            chat_data = {
                "message": "Test message with fallback memory",
                "session_id": "error_handling_session",
                "context": {"user_id": "error_handling_user"}
            }

            chat_response = client.post(f"/agent-factory/agents/{agent_id}/chat", json=chat_data)
            assert chat_response.status_code == 200

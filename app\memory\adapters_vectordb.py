"""
VectorDB Storage Adapter for AgentKit Memory

Provides semantic search and embedding storage for semantic memory.
"""
from typing import Any, List, Optional
import logging
import requests
import json
import uuid
import hashlib
from .adapters import BaseMemoryAdapter

# Qdrant client and models
try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct
    from qdrant_client.http.exceptions import UnexpectedResponse
except ImportError:
    QdrantClient = None
    Distance = None
    VectorParams = None
    PointStruct = None
    UnexpectedResponse = None

logger = logging.getLogger(__name__)

class VectorDBAdapter(BaseMemoryAdapter):
    """
    VectorDB adapter for semantic memory (embeddings, semantic search).

    This adapter provides semantic search capabilities using Qdrant vector database
    for storing and retrieving embeddings with similarity search.

    Args:
        url: Qdrant server URL (default: "http://localhost:6333")
        api_key: Optional API key for authentication
        collection: Collection name for storing vectors (default: "default_collection")
        vector_size: Size of the vectors (default: 768)
        distance: Distance metric for similarity search (default: "Cosine")
        **kwargs: Additional arguments passed to QdrantClient

    Raises:
        ImportError: If qdrant-client is not installed
        ConnectionError: If cannot connect to Qdrant server
    """
    def __init__(
        self,
        url: str = "http://localhost:6333",
        api_key: Optional[str] = None,
        collection: str = "default_collection",
        vector_size: int = 768,
        distance: str = "Cosine",
        **kwargs
    ):
        if QdrantClient is None:
            raise ImportError(
                "qdrant-client is not installed. "
                "Install it with: pip install qdrant-client"
            )

        self.collection = collection
        self.vector_size = vector_size
        self.distance = distance

        # Store URL for HTTP fallback
        self.base_url = url
        self.api_key = api_key

        try:
            # Initialize Qdrant client with explicit HTTP configuration
            if url.startswith("http"):
                # For HTTP connections, use host and port
                import urllib.parse
                parsed_url = urllib.parse.urlparse(url)
                host = parsed_url.hostname or "localhost"
                port = parsed_url.port or 6333

                # Use standard client with prefer_grpc=False
                self.client = QdrantClient(
                    host=host,
                    port=port,
                    api_key=api_key,
                    prefer_grpc=False,
                    **kwargs
                )
                logger.info(f"Initialized Qdrant client for {host}:{port}")
            else:
                # For non-HTTP URLs, use standard client
                self.client = QdrantClient(
                    url=url,
                    api_key=api_key,
                    prefer_grpc=False,
                    **kwargs
                )
                logger.info(f"Initialized Qdrant client for {url}")

        except Exception as e:
            logger.error(f"Failed to initialize Qdrant client for {url}: {e}")
            raise ConnectionError(f"Cannot initialize Qdrant client for {url}: {e}")

        self._init_collection()

    def _convert_key_to_id(self, key: str) -> str:
        """
        Convert a string key to a valid Qdrant point ID.

        Qdrant accepts either integers or UUIDs as point IDs.
        This method converts string keys to deterministic UUIDs.

        Args:
            key: String key to convert

        Returns:
            UUID string that can be used as Qdrant point ID
        """
        # Create a deterministic UUID from the key using MD5 hash
        key_hash = hashlib.md5(key.encode('utf-8')).hexdigest()
        # Convert hash to UUID format
        uuid_str = f"{key_hash[:8]}-{key_hash[8:12]}-{key_hash[12:16]}-{key_hash[16:20]}-{key_hash[20:32]}"
        return uuid_str

    def _http_get_collections(self):
        """Get collections using direct HTTP request."""
        try:
            headers = {}
            if self.api_key:
                headers["api-key"] = self.api_key

            response = requests.get(f"{self.base_url}/collections", headers=headers)
            response.raise_for_status()
            return response.json()["result"]["collections"]
        except Exception as e:
            logger.error(f"HTTP get collections failed: {e}")
            raise

    def _http_create_collection(self):
        """Create collection using direct HTTP request."""
        try:
            headers = {"Content-Type": "application/json"}
            if self.api_key:
                headers["api-key"] = self.api_key

            collection_config = {
                "vectors": {
                    "size": self.vector_size,
                    "distance": self.distance
                }
            }

            response = requests.put(
                f"{self.base_url}/collections/{self.collection}",
                headers=headers,
                json=collection_config
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"HTTP create collection failed: {e}")
            raise

    def _init_collection(self):
        """
        Initialize collection if it doesn't exist.

        Creates a new collection with the specified vector configuration
        if it doesn't already exist in the Qdrant instance.

        Raises:
            Exception: If collection creation fails
        """
        try:
            # Try using the Qdrant client first
            collections_response = self.client.get_collections()
            existing_collections = [col.name for col in collections_response.collections]

            if self.collection not in existing_collections:
                logger.info(f"Creating collection '{self.collection}' with vector size {self.vector_size}")

                # Create collection with proper vector configuration
                self.client.create_collection(
                    collection_name=self.collection,
                    vectors_config=VectorParams(
                        size=self.vector_size,
                        distance=getattr(Distance, self.distance.upper())
                    )
                )
                logger.info(f"Collection '{self.collection}' created successfully")
            else:
                logger.info(f"Collection '{self.collection}' already exists")

        except NotImplementedError:
            # Fallback to HTTP requests if client methods are not implemented
            logger.warning("Qdrant client methods not implemented, using HTTP fallback")
            try:
                existing_collections = self._http_get_collections()
                collection_names = [col["name"] for col in existing_collections]

                if self.collection not in collection_names:
                    logger.info(f"Creating collection '{self.collection}' via HTTP with vector size {self.vector_size}")
                    self._http_create_collection()
                    logger.info(f"Collection '{self.collection}' created successfully via HTTP")
                else:
                    logger.info(f"Collection '{self.collection}' already exists")

            except Exception as http_error:
                logger.error(f"HTTP fallback failed: {http_error}")
                raise

        except UnexpectedResponse as e:
            if "already exists" in str(e).lower():
                logger.info(f"Collection '{self.collection}' already exists")
            else:
                logger.error(f"Unexpected response when creating collection: {e}")
                raise
        except Exception as e:
            logger.error(f"Failed to initialize collection '{self.collection}': {e}")
            # Log more details about the error
            logger.error(f"Error type: {type(e).__name__}")
            logger.error(f"Error details: {str(e)}")
            raise
    def _http_add_vector(self, key: str, embedding: List[float], payload: dict) -> None:
        """Add vector using direct HTTP request."""
        try:
            headers = {"Content-Type": "application/json"}
            if self.api_key:
                headers["api-key"] = self.api_key

            # Convert key to valid Qdrant ID and store original key in payload
            point_id = self._convert_key_to_id(key)
            enhanced_payload = payload.copy()
            enhanced_payload["_original_key"] = key  # Store original key for retrieval

            point_data = {
                "points": [
                    {
                        "id": point_id,
                        "vector": embedding,
                        "payload": enhanced_payload
                    }
                ]
            }

            response = requests.put(
                f"{self.base_url}/collections/{self.collection}/points",
                headers=headers,
                json=point_data
            )
            if response.status_code != 200:
                logger.error(f"HTTP add vector failed with status {response.status_code}: {response.text}")
            response.raise_for_status()
        except Exception as e:
            logger.error(f"HTTP add vector failed: {e}")
            raise

    def _add_vector_original(self, key: str, embedding: List[float], payload: dict) -> None:
        """
        Add a vector with payload to the collection (original implementation).

        Args:
            key: Unique identifier for the vector
            embedding: Vector embedding as list of floats
            payload: Additional metadata to store with the vector

        Raises:
            ValueError: If embedding size doesn't match collection configuration
            Exception: If upsert operation fails
        """
        try:
            if len(embedding) != self.vector_size:
                raise ValueError(
                    f"Embedding size {len(embedding)} doesn't match "
                    f"collection vector size {self.vector_size}"
                )

            # Convert key to valid Qdrant ID and store original key in payload
            point_id = self._convert_key_to_id(key)
            enhanced_payload = payload.copy()
            enhanced_payload["_original_key"] = key  # Store original key for retrieval

            # Create point with proper structure
            point = PointStruct(
                id=point_id,
                vector=embedding,
                payload=enhanced_payload
            )

            self.client.upsert(
                collection_name=self.collection,
                points=[point]
            )
            logger.debug(f"Added vector with key '{key}' to collection '{self.collection}'")

        except NotImplementedError:
            # Fallback to HTTP request
            logger.warning("Using HTTP fallback for add vector")
            try:
                self._http_add_vector(key, embedding, payload)
                logger.debug(f"Added vector with key '{key}' via HTTP to collection '{self.collection}'")
            except Exception as http_error:
                logger.error(f"HTTP fallback failed: {http_error}")
                raise
        except Exception as e:
            logger.error(f"Failed to add vector with key '{key}': {e}")
            raise

    def _http_search_vectors(self, embedding: List[float], limit: int = 5) -> List[Any]:
        """Search vectors using direct HTTP request."""
        try:
            headers = {"Content-Type": "application/json"}
            if self.api_key:
                headers["api-key"] = self.api_key

            search_data = {
                "vector": embedding,
                "limit": limit,
                "with_payload": True
            }

            response = requests.post(
                f"{self.base_url}/collections/{self.collection}/points/search",
                headers=headers,
                json=search_data
            )
            response.raise_for_status()

            results = response.json()["result"]
            return [result["payload"] for result in results]
        except Exception as e:
            logger.error(f"HTTP search vectors failed: {e}")
            raise

    def search(self, embedding: List[float], limit: int = 5) -> List[Any]:
        """
        Search for similar vectors in the collection.

        Args:
            embedding: Query vector as list of floats
            limit: Maximum number of results to return

        Returns:
            List of payloads from similar vectors, ordered by similarity

        Raises:
            ValueError: If embedding size doesn't match collection configuration
            Exception: If search operation fails
        """
        try:
            if len(embedding) != self.vector_size:
                raise ValueError(
                    f"Query embedding size {len(embedding)} doesn't match "
                    f"collection vector size {self.vector_size}"
                )

            results = self.client.search(
                collection_name=self.collection,
                query_vector=embedding,
                limit=limit
            )

            payloads = [result.payload for result in results]
            logger.debug(f"Found {len(payloads)} similar vectors in collection '{self.collection}'")
            return payloads

        except NotImplementedError:
            # Fallback to HTTP request
            logger.warning("Using HTTP fallback for search vectors")
            try:
                payloads = self._http_search_vectors(embedding, limit)
                logger.debug(f"Found {len(payloads)} similar vectors via HTTP in collection '{self.collection}'")
                return payloads
            except Exception as http_error:
                logger.error(f"HTTP fallback failed: {http_error}")
                raise
        except Exception as e:
            logger.error(f"Failed to search vectors: {e}")
            raise

    def _http_clear_collection(self) -> None:
        """Clear collection using direct HTTP request."""
        try:
            headers = {}
            if self.api_key:
                headers["api-key"] = self.api_key

            # Delete collection
            response = requests.delete(f"{self.base_url}/collections/{self.collection}", headers=headers)
            response.raise_for_status()

            # Recreate collection
            self._http_create_collection()
        except Exception as e:
            logger.error(f"HTTP clear collection failed: {e}")
            raise

    def _clear_collection_original(self) -> None:
        """
        Delete the entire collection and recreate it (original implementation).

        This will remove all vectors and their payloads from the collection.

        Raises:
            Exception: If collection deletion or recreation fails
        """
        try:
            logger.info(f"Clearing collection '{self.collection}'")
            self.client.delete_collection(self.collection)
            self._init_collection()
            logger.info(f"Collection '{self.collection}' cleared and recreated")

        except NotImplementedError:
            # Fallback to HTTP request
            logger.warning("Using HTTP fallback for clear collection")
            try:
                self._http_clear_collection()
                logger.info(f"Collection '{self.collection}' cleared and recreated via HTTP")
            except Exception as http_error:
                logger.error(f"HTTP fallback failed: {http_error}")
                raise
        except Exception as e:
            logger.error(f"Failed to clear collection '{self.collection}': {e}")
            raise

    def _http_get_collection_info(self) -> dict:
        """Get collection info using direct HTTP request."""
        try:
            headers = {}
            if self.api_key:
                headers["api-key"] = self.api_key

            response = requests.get(f"{self.base_url}/collections/{self.collection}", headers=headers)
            response.raise_for_status()
            return response.json()["result"]
        except Exception as e:
            logger.error(f"HTTP get collection info failed: {e}")
            raise

    def get_collection_info(self) -> dict:
        """
        Get information about the collection.

        Returns:
            Dictionary containing collection information

        Raises:
            Exception: If unable to retrieve collection info
        """
        try:
            info = self.client.get_collection(self.collection)
            return {
                "name": self.collection,
                "vectors_count": info.vectors_count,
                "indexed_vectors_count": info.indexed_vectors_count,
                "points_count": info.points_count,
                "status": info.status
            }
        except NotImplementedError:
            # Fallback to HTTP request
            logger.warning("Using HTTP fallback for get_collection_info")
            try:
                info = self._http_get_collection_info()
                return {
                    "name": self.collection,
                    "vectors_count": info.get("vectors_count", 0),
                    "indexed_vectors_count": info.get("indexed_vectors_count", 0),
                    "points_count": info.get("points_count", 0),
                    "status": info.get("status", "unknown")
                }
            except Exception as http_error:
                logger.error(f"HTTP fallback failed: {http_error}")
                raise
        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            raise

    def delete_vector(self, key: str) -> None:
        """
        Delete a specific vector by its key.

        Args:
            key: Unique identifier of the vector to delete

        Raises:
            Exception: If deletion fails
        """
        try:
            self.client.delete(
                collection_name=self.collection,
                points_selector=[key]
            )
            logger.debug(f"Deleted vector with key '{key}' from collection '{self.collection}'")

        except Exception as e:
            logger.error(f"Failed to delete vector with key '{key}': {e}")
            raise

    # BaseMemoryAdapter interface methods
    def add(self, key: str, item: Any) -> None:
        """
        Add an item to vector storage (BaseMemoryAdapter interface).

        For vector storage, items should contain 'embedding' and optional 'payload'.
        If no embedding is provided, this will store the item as metadata only.

        Args:
            key: Storage key
            item: Item to store (should contain 'embedding' and optional 'payload')
        """
        try:
            if isinstance(item, dict):
                embedding = item.get('embedding')
                payload = item.get('payload', item)

                if embedding and isinstance(embedding, list):
                    # Store as vector with embedding
                    self.add_vector(key, embedding, payload)
                else:
                    # Store as metadata only (no vector search capability)
                    metadata = {
                        'key': key,
                        'data': item,
                        'timestamp': item.get('timestamp', None)
                    }
                    # Use a dummy embedding for storage
                    dummy_embedding = [0.0] * self.vector_size
                    self.add_vector(key, dummy_embedding, metadata)
            else:
                # Store non-dict items as metadata
                metadata = {
                    'key': key,
                    'data': item,
                    'timestamp': getattr(item, 'timestamp', None)
                }
                dummy_embedding = [0.0] * self.vector_size
                self.add_vector(key, dummy_embedding, metadata)

        except Exception as e:
            logger.error(f"Failed to add item to vector storage: {e}")
            raise

    def get(self, key: str, limit: Optional[int] = None) -> List[Any]:
        """
        Retrieve items from vector storage (BaseMemoryAdapter interface).

        Note: This method cannot perform semantic search without a query embedding.
        It will return empty list as vector databases are optimized for similarity search.
        Use search() method with embeddings for proper vector retrieval.

        Args:
            key: Storage key
            limit: Maximum number of items to retrieve

        Returns:
            Empty list (vector databases require embeddings for retrieval)
        """
        logger.warning(
            "VectorDBAdapter.get() called without embedding. "
            "Vector databases require embeddings for retrieval. "
            "Use search() method with query embedding instead."
        )
        return []

    def clear(self, key: str) -> None:
        """
        Clear items for a specific key (BaseMemoryAdapter interface).

        Note: This clears the entire collection as vector databases
        don't typically support key-based deletion without embeddings.

        Args:
            key: Storage key to clear (ignored, clears entire collection)
        """
        logger.warning(
            f"VectorDBAdapter.clear() called for key '{key}'. "
            "Clearing entire collection as vector databases don't support key-based clearing."
        )
        self.clear_collection()

    def add_vector(self, key: str, embedding: List[float], payload: dict) -> None:
        """
        Add a vector with payload to the collection (original VectorDB method).

        Args:
            key: Unique identifier for the vector
            embedding: Vector embedding as list of floats
            payload: Additional metadata to store with the vector
        """
        # Delegate to the original implementation
        self._add_vector_original(key, embedding, payload)

    def clear_collection(self) -> None:
        """
        Delete the entire collection and recreate it (original VectorDB method).

        This will remove all vectors and their payloads from the collection.
        """
        # Delegate to the original implementation
        self._clear_collection_original()

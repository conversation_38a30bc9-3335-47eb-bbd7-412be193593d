#!/usr/bin/env python3
"""
Test script for the create agent API endpoint.
"""

import requests
import json
import time

def test_create_agent_api():
    """Test the create agent API endpoint."""
    print("Testing Create Agent API...")
    
    # API endpoint
    url = "http://localhost:8000/agent-factory/agents/create"
    
    # Request payload
    payload = {
        "agent_type": "customer_support",
        "config": {
            "additionalProp1": {}
        },
        "name": "<PERSON>"
    }
    
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json"
    }
    
    try:
        print(f"Making POST request to: {url}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"Response Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("✓ API call successful!")
            print(f"Response: {json.dumps(response_data, indent=2)}")
            return True
        else:
            print(f"❌ API call failed with status {response.status_code}")
            print(f"Response text: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Is the server running?")
        print("Try starting the server with: python start_server.py")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def check_server_health():
    """Check if the server is running."""
    try:
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code == 200:
            print("✓ Server is running and accessible")
            return True
        else:
            print(f"⚠️ Server responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("AGENT CREATION API TEST")
    print("=" * 60)
    
    # Check server health first
    print("1. Checking server health...")
    if check_server_health():
        print("\n2. Testing create agent API...")
        success = test_create_agent_api()
        
        if success:
            print("\n🎉 API test completed successfully!")
        else:
            print("\n❌ API test failed!")
    else:
        print("\n❌ Server is not running. Please start the server first.")
        print("Run: python start_server.py")

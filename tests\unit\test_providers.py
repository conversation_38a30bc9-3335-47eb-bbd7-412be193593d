"""
Unit tests for providers module.

This module contains comprehensive unit tests for all LLM provider implementations
including factory, OpenAI, Ollama, and base provider functionality.
"""

import pytest
import os
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

from app.providers.base import LLMProvider
from app.providers.factory import get_llm, ProviderLoadError
from app.providers.openai_provider import OpenAIProvider
from app.providers.ollama_provider import OllamaProvider
from app.core.types import Message


class TestProviderFactory:
    """Test cases for provider factory."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Clear environment variables
        self.original_env = {}
        for key in ["LLM_PROVIDER", "OPENAI_API_KEY", "OLLAMA_BASE_URL"]:
            self.original_env[key] = os.environ.get(key)
            if key in os.environ:
                del os.environ[key]
    
    def teardown_method(self):
        """Clean up test fixtures."""
        # Restore environment variables
        for key, value in self.original_env.items():
            if value is not None:
                os.environ[key] = value
            elif key in os.environ:
                del os.environ[key]
    
    def test_get_llm_no_provider_set(self):
        """Test get_llm when no provider is set."""
        with pytest.raises(ProviderLoadError) as exc_info:
            get_llm()
        
        assert "LLM_PROVIDER environment variable not set" in str(exc_info.value)
    
    def test_get_llm_openai_provider(self):
        """Test get_llm with OpenAI provider."""
        os.environ["LLM_PROVIDER"] = "openai"
        os.environ["OPENAI_API_KEY"] = "test-api-key"
        
        with patch('app.providers.openai_provider.OpenAI'):
            provider = get_llm()
            assert isinstance(provider, OpenAIProvider)
    
    def test_get_llm_ollama_provider(self):
        """Test get_llm with Ollama provider."""
        os.environ["LLM_PROVIDER"] = "ollama"
        os.environ["OLLAMA_BASE_URL"] = "http://localhost:11434"
        
        with patch('app.providers.ollama_provider.Client'):
            provider = get_llm()
            assert isinstance(provider, OllamaProvider)
    
    def test_get_llm_unsupported_provider(self):
        """Test get_llm with unsupported provider."""
        os.environ["LLM_PROVIDER"] = "unsupported_provider"
        
        with pytest.raises(ProviderLoadError) as exc_info:
            get_llm()
        
        assert "Unsupported LLM provider" in str(exc_info.value)
        assert "unsupported_provider" in str(exc_info.value)
    
    def test_get_llm_openai_missing_api_key(self):
        """Test get_llm with OpenAI provider but missing API key."""
        os.environ["LLM_PROVIDER"] = "openai"
        # Don't set OPENAI_API_KEY
        
        with pytest.raises(ProviderLoadError) as exc_info:
            get_llm()
        
        assert "OPENAI_API_KEY environment variable not set" in str(exc_info.value)
    
    def test_get_llm_ollama_missing_base_url(self):
        """Test get_llm with Ollama provider but missing base URL."""
        os.environ["LLM_PROVIDER"] = "ollama"
        # Don't set OLLAMA_BASE_URL
        
        with pytest.raises(ProviderLoadError) as exc_info:
            get_llm()
        
        assert "OLLAMA_BASE_URL environment variable not set" in str(exc_info.value)


class TestOpenAIProvider:
    """Test cases for OpenAIProvider class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.api_key = "test-api-key"
        self.model = "gpt-3.5-turbo"
        
        with patch('app.providers.openai_provider.OpenAI') as mock_openai:
            self.mock_client = Mock()
            mock_openai.return_value = self.mock_client
            self.provider = OpenAIProvider(api_key=self.api_key, model=self.model)
    
    def test_initialization(self):
        """Test OpenAIProvider initialization."""
        assert self.provider.model == self.model
        assert hasattr(self.provider, 'client')
    
    def test_initialization_default_model(self):
        """Test OpenAIProvider initialization with default model."""
        with patch('app.providers.openai_provider.OpenAI') as mock_openai:
            mock_client = Mock()
            mock_openai.return_value = mock_client
            provider = OpenAIProvider(api_key=self.api_key)
            
            assert provider.model == "gpt-3.5-turbo"  # Default model
    
    def test_generate_successful_response(self):
        """Test successful response generation."""
        messages = [
            Message(role="user", content="Hello, how are you?")
        ]
        
        # Mock OpenAI response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "I'm doing well, thank you!"
        mock_response.usage.total_tokens = 25
        
        self.mock_client.chat.completions.create.return_value = mock_response
        
        response = self.provider.generate(messages)
        
        assert response.content == "I'm doing well, thank you!"
        assert response.usage["total_tokens"] == 25
        assert response.model == self.model
        
        # Verify API call
        self.mock_client.chat.completions.create.assert_called_once()
        call_args = self.mock_client.chat.completions.create.call_args
        assert call_args[1]["model"] == self.model
        assert len(call_args[1]["messages"]) == 1
        assert call_args[1]["messages"][0]["role"] == "user"
        assert call_args[1]["messages"][0]["content"] == "Hello, how are you?"
    
    def test_generate_with_multiple_messages(self):
        """Test generation with multiple messages."""
        messages = [
            Message(role="user", content="Hello"),
            Message(role="assistant", content="Hi there!"),
            Message(role="user", content="How are you?")
        ]
        
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "I'm doing great!"
        mock_response.usage.total_tokens = 30
        
        self.mock_client.chat.completions.create.return_value = mock_response
        
        response = self.provider.generate(messages)
        
        assert response.content == "I'm doing great!"
        
        # Verify all messages were sent
        call_args = self.mock_client.chat.completions.create.call_args
        assert len(call_args[1]["messages"]) == 3
    
    def test_generate_api_error(self):
        """Test handling of API errors."""
        messages = [Message(role="user", content="Test message")]
        
        # Mock API error
        self.mock_client.chat.completions.create.side_effect = Exception("API Error")
        
        with pytest.raises(Exception) as exc_info:
            self.provider.generate(messages)
        
        assert "API Error" in str(exc_info.value)
    
    def test_generate_with_custom_parameters(self):
        """Test generation with custom parameters."""
        messages = [Message(role="user", content="Test")]
        
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Response"
        mock_response.usage.total_tokens = 10
        
        self.mock_client.chat.completions.create.return_value = mock_response
        
        # Test with custom temperature
        response = self.provider.generate(messages, temperature=0.8)
        
        call_args = self.mock_client.chat.completions.create.call_args
        assert call_args[1]["temperature"] == 0.8
    
    def test_message_conversion(self):
        """Test conversion of Message objects to OpenAI format."""
        messages = [
            Message(role="system", content="You are a helpful assistant"),
            Message(role="user", content="Hello"),
            Message(role="assistant", content="Hi there!")
        ]
        
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Response"
        mock_response.usage.total_tokens = 15
        
        self.mock_client.chat.completions.create.return_value = mock_response
        
        self.provider.generate(messages)
        
        call_args = self.mock_client.chat.completions.create.call_args
        openai_messages = call_args[1]["messages"]
        
        assert len(openai_messages) == 3
        assert openai_messages[0]["role"] == "system"
        assert openai_messages[0]["content"] == "You are a helpful assistant"
        assert openai_messages[1]["role"] == "user"
        assert openai_messages[1]["content"] == "Hello"
        assert openai_messages[2]["role"] == "assistant"
        assert openai_messages[2]["content"] == "Hi there!"


class TestOllamaProvider:
    """Test cases for OllamaProvider class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.base_url = "http://localhost:11434"
        self.model = "llama2"
        
        with patch('app.providers.ollama_provider.Client') as mock_client_class:
            self.mock_client = Mock()
            mock_client_class.return_value = self.mock_client
            self.provider = OllamaProvider(base_url=self.base_url, model=self.model)
    
    def test_initialization(self):
        """Test OllamaProvider initialization."""
        assert self.provider.model == self.model
        assert hasattr(self.provider, 'client')
    
    def test_initialization_default_model(self):
        """Test OllamaProvider initialization with default model."""
        with patch('app.providers.ollama_provider.Client') as mock_client_class:
            mock_client = Mock()
            mock_client_class.return_value = mock_client
            provider = OllamaProvider(base_url=self.base_url)
            
            assert provider.model == "llama2"  # Default model
    
    def test_generate_successful_response(self):
        """Test successful response generation."""
        messages = [
            Message(role="user", content="Hello, how are you?")
        ]
        
        # Mock Ollama response
        mock_response = {
            'message': {
                'content': "I'm doing well, thank you!"
            },
            'done': True,
            'total_duration': **********,  # 1 second in nanoseconds
            'load_duration': 100000000,
            'prompt_eval_count': 10,
            'eval_count': 15
        }
        
        self.mock_client.chat.return_value = mock_response
        
        response = self.provider.generate(messages)
        
        assert response.content == "I'm doing well, thank you!"
        assert response.model == self.model
        assert "total_duration" in response.usage
        
        # Verify API call
        self.mock_client.chat.assert_called_once()
        call_args = self.mock_client.chat.call_args
        assert call_args[1]["model"] == self.model
        assert len(call_args[1]["messages"]) == 1
    
    def test_generate_with_stream_false(self):
        """Test generation with streaming disabled."""
        messages = [Message(role="user", content="Test")]
        
        mock_response = {
            'message': {'content': "Response"},
            'done': True
        }
        
        self.mock_client.chat.return_value = mock_response
        
        response = self.provider.generate(messages)
        
        call_args = self.mock_client.chat.call_args
        assert call_args[1]["stream"] is False
    
    def test_generate_api_error(self):
        """Test handling of API errors."""
        messages = [Message(role="user", content="Test message")]
        
        # Mock API error
        self.mock_client.chat.side_effect = Exception("Ollama API Error")
        
        with pytest.raises(Exception) as exc_info:
            self.provider.generate(messages)
        
        assert "Ollama API Error" in str(exc_info.value)
    
    def test_message_conversion(self):
        """Test conversion of Message objects to Ollama format."""
        messages = [
            Message(role="user", content="Hello"),
            Message(role="assistant", content="Hi there!")
        ]
        
        mock_response = {
            'message': {'content': "Response"},
            'done': True
        }
        
        self.mock_client.chat.return_value = mock_response
        
        self.provider.generate(messages)
        
        call_args = self.mock_client.chat.call_args
        ollama_messages = call_args[1]["messages"]
        
        assert len(ollama_messages) == 2
        assert ollama_messages[0]["role"] == "user"
        assert ollama_messages[0]["content"] == "Hello"
        assert ollama_messages[1]["role"] == "assistant"
        assert ollama_messages[1]["content"] == "Hi there!"


class TestLLMProviderBase:
    """Test cases for base LLMProvider functionality."""
    
    def test_provider_interface(self):
        """Test that LLMProvider is an abstract base class."""
        # Should not be able to instantiate LLMProvider directly
        with pytest.raises(TypeError):
            LLMProvider()
    
    def test_custom_provider_implementation(self):
        """Test custom provider implementation."""
        class CustomProvider(LLMProvider):
            def __init__(self, custom_param):
                self.custom_param = custom_param
            
            def generate(self, messages, **kwargs):
                from app.core.types import LLMResponse
                return LLMResponse(
                    content=f"Custom response with {self.custom_param}",
                    model="custom-model",
                    usage={"tokens": 10}
                )
        
        provider = CustomProvider("test_param")
        messages = [Message(role="user", content="Test")]
        
        response = provider.generate(messages)
        
        assert "Custom response with test_param" in response.content
        assert response.model == "custom-model"
        assert response.usage["tokens"] == 10

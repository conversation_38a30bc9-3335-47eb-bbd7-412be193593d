
from __future__ import annotations
from typing import Any, Dict, List, Optional, Protocol, Union
from enum import Enum
from pydantic import BaseModel, Field

class ContentType(Enum):
    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"

class MultiModalContent(BaseModel):
    type: ContentType
    data: Union[str, bytes]
    metadata: Dict[str, Any] = Field(default_factory=dict)

class Message(BaseModel):
    role: str  # "user" | "assistant" | "system" | "tool"
    content: Union[str, List[MultiModalContent]]
    meta: Dict[str, Any] = Field(default_factory=dict)

class Action(BaseModel):
    tool: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    reason: str = ""

class Plan(BaseModel):
    actions: List[Action] = Field(default_factory=list)
    rationale: str = ""

class Observation(BaseModel):
    tool: str
    output: Any
    ok: bool = True
    error: Optional[str] = None

class LLMResponse(BaseModel):
    """Response from an LLM provider."""
    content: str
    model: str
    usage: Dict[str, Any] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)

class Planner(Protocol):
    def plan(self, goal: str, history: List[Message], state: Dict[str, Any]) -> Plan: ...

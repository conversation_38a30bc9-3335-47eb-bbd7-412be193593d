from __future__ import annotations
from typing import Dict
from .base import Tool

class ToolRegistry:
    def __init__(self) -> None:
        self._t: Dict[str, Tool] = {}
    def register(self, tool: Tool) -> None:
        self._t[tool.spec.name] = tool
    def get(self, name: str) -> Tool:
        if name not in self._t: raise KeyError(f"Tool not found: {name}")
        return self._t[name]
    def list(self): return list(self._t.values())

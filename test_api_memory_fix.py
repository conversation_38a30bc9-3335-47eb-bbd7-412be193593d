"""
Test script to verify the API memory fix is working.
"""

import requests
import json
import time

def test_create_agent_api():
    """Test agent creation API with memory configuration."""
    print("Testing Agent Creation API...")
    
    agent_data = {
        "agent_type": "customer_support",
        "name": "MemoryFixTestAgent",
        "config": {
            "memory": {
                "short_term": {"adapter": "in_memory"},
                "episodic": {"adapter": "in_memory"},
                "procedural": {"adapter": "in_memory"},
                "semantic": {"adapter": "in_memory"}
            },
            "tools": [],
            "planner": {}
        }
    }

    try:
        response = requests.post(
            'http://localhost:8000/agent-factory/agents/create',
            json=agent_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            response_data = response.json()
            agent_id = response_data.get("agent_id")
            print(f"✓ Agent created successfully with ID: {agent_id}")
            return agent_id
        else:
            print(f"✗ Agent creation failed: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Request failed: {e}")
        return None

def test_chat_api(agent_id):
    """Test chat API with the created agent."""
    if not agent_id:
        print("✗ Cannot test chat API without agent ID")
        return False
        
    print(f"\nTesting Chat API with agent {agent_id}...")
    
    chat_data = {
        "message": "Hello, I need help with my account",
        "session_id": "test_session_001",
        "context": {
            "user_id": "test_user",
            "timestamp": time.time()
        }
    }

    try:
        response = requests.post(
            f'http://localhost:8000/agent-factory/agents/{agent_id}/chat',
            json=chat_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("✓ Chat API works successfully")
            print(f"Agent Response: {response_data.get('response', 'No response')}")
            return True
        else:
            print(f"✗ Chat API failed: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Chat request failed: {e}")
        return False

def test_server_health():
    """Test if server is running."""
    print("Testing Server Health...")
    
    try:
        response = requests.get('http://localhost:8000/health', timeout=5)
        print(f"Health Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ Server is running")
            return True
        else:
            print("✗ Server health check failed")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Server is not running: {e}")
        return False

def main():
    """Run API tests."""
    print("=" * 60)
    print("API MEMORY FIX VERIFICATION")
    print("=" * 60)
    
    # Test server health
    if not test_server_health():
        print("\n❌ Server is not running. Please start the server first:")
        print("python -m uvicorn app.api.main:app --host 0.0.0.0 --port 8000 --reload")
        return False
    
    # Test agent creation
    agent_id = test_create_agent_api()
    
    # Test chat functionality
    chat_success = test_chat_api(agent_id)
    
    print("\n" + "=" * 60)
    if agent_id and chat_success:
        print("🎉 All API tests passed! Memory fix is working correctly.")
        print("The original error 'InMemoryAdapter' object has no attribute 'add_message' is FIXED!")
        return True
    else:
        print("❌ Some API tests failed.")
        return False

if __name__ == "__main__":
    main()

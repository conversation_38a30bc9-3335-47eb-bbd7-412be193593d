#!/usr/bin/env python3
"""
Test script to verify Qdrant connection and VectorDBAdapter functionality.
"""

import sys
import traceback
from app.memory.adapters_vectordb import VectorDBAdapter

def test_qdrant_connection():
    """Test Qdrant connection and basic operations."""
    print("Testing Qdrant connection...")
    
    try:
        # Test VectorDBAdapter initialization
        print("1. Initializing VectorDBAdapter...")
        adapter = VectorDBAdapter(
            url="http://localhost:6333",
            collection="test_collection",
            vector_size=768
        )
        print("✓ VectorDBAdapter initialized successfully")
        
        # Test collection info
        print("2. Getting collection info...")
        info = adapter.get_collection_info()
        print(f"✓ Collection info: {info}")
        
        # Test adding a vector
        print("3. Adding test vector...")
        test_embedding = [0.1] * 768  # Simple test vector
        test_payload = {"text": "test document", "category": "test"}
        adapter.add("test_key_1", test_embedding, test_payload)
        print("✓ Test vector added successfully")
        
        # Test search
        print("4. Testing vector search...")
        query_embedding = [0.1] * 768  # Same vector for exact match
        results = adapter.search(query_embedding, limit=1)
        print(f"✓ Search results: {results}")
        
        # Test clearing collection
        print("5. Clearing collection...")
        adapter.clear()
        print("✓ Collection cleared successfully")
        
        print("\n🎉 All tests passed! Qdrant is working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        print(f"Error type: {type(e).__name__}")
        print(f"Traceback:\n{traceback.format_exc()}")
        return False

def test_agent_creation():
    """Test agent creation with VectorDB memory."""
    print("\nTesting agent creation with VectorDB memory...")
    
    try:
        from app.factory.agent_factory import AgentFactory
        
        # Create agent with custom config that uses VectorDB
        config = {
            "memory": {
                "semantic": {
                    "adapter": "vectordb",
                    "url": "http://localhost:6333",
                    "collection": "agent_test_collection"
                }
            }
        }
        
        print("Creating customer support agent...")
        agent = AgentFactory.create_agent(
            agent_type="customer_support",
            config=config,
            name="test_agent"
        )
        print("✓ Agent created successfully")
        print(f"Agent capabilities: {agent.capabilities}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent creation failed: {e}")
        print(f"Error type: {type(e).__name__}")
        print(f"Traceback:\n{traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("QDRANT CONNECTION AND AGENT CREATION TEST")
    print("=" * 60)
    
    # Test 1: Qdrant connection
    qdrant_success = test_qdrant_connection()
    
    # Test 2: Agent creation (only if Qdrant works)
    if qdrant_success:
        agent_success = test_agent_creation()
        
        if agent_success:
            print("\n🎉 ALL TESTS PASSED! Ready to test the API.")
        else:
            print("\n⚠️  Qdrant works but agent creation failed.")
            sys.exit(1)
    else:
        print("\n❌ Qdrant connection failed. Please check your Qdrant server.")
        sys.exit(1)

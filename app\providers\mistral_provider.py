from __future__ import annotations
import os, json
from typing import Any
from .base import LLM

class MistralProvider(LLM):
    """
    Mistral provider using the official `mistralai` SDK.

    Env:
      - MISTRAL_API_KEY
      - MISTRAL_MODEL (default: mistral-large-latest)

    Install:
      pip install "mistralai>=0.4.0"
    """
    def __init__(self, model_env: str = "MISTRAL_MODEL", api_key_env: str = "MISTRAL_API_KEY"):
        try:
            from mistralai import MistralClient
            from mistralai.models.chat_completion import ChatMessage
        except Exception as e:
            raise RuntimeError("Install mistral: pip install 'mistralai>=0.4.0'") from e

        api_key = os.getenv(api_key_env)
        if not api_key:
            raise RuntimeError(f"{api_key_env} is not set")
        self.client = MistralClient(api_key=api_key)
        self.ChatMessage = ChatMessage
        self.model = os.getenv(model_env, "mistral-large-latest")

    def generate(self, prompt: str) -> str:
        # Instruct the model to return JSON only
        sys = "You are a planner. Reply with STRICT JSON only, no prose."
        msgs = [
            self.ChatMessage(role="system", content=sys),
            self.ChatMessage(role="user", content=prompt),
        ]
        resp = self.client.chat(model=self.model, messages=msgs, temperature=0.2)
        content = resp.choices[0].message.content
        if isinstance(content, list):
            # multi-part segments; join text
            text = "".join([seg.get("text", "") if isinstance(seg, dict) else str(seg) for seg in content])
            return text
        return content

    def parse_json(self, text: str) -> Any:
        # use base default if you want, but keep a tolerant fallback here
        try:
            return json.loads(text)
        except Exception:
            # Try to find the first/last curly braces (simple JSON rescue)
            start, end = text.find("{"), text.rfind("}")
            if start != -1 and end != -1 and end > start:
                return json.loads(text[start:end+1])
            raise

"""
Customer Support Agent implementation.

This module provides a specialized agent for handling customer support
queries, ticket management, and knowledge base interactions.
"""

from typing import Dict, Any, List, Optional
from loguru import logger
from .base import BaseAgent
from ..core.exceptions import AgentError


class CustomerSupportAgent(BaseAgent):
    """
    Specialized agent for customer support operations.
    
    This agent is designed to handle customer inquiries, manage support
    tickets, search knowledge bases, and escalate issues when necessary.
    
    Capabilities:
        - Ticket creation and management
        - Knowledge base search
        - Issue classification
        - Escalation handling
        - Customer interaction
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Customer Support Agent.

        Args:
            config: Configuration dictionary containing agent parameters
        """
        # Use custom name if provided, otherwise use default
        agent_name = config.get("custom_name", "customer_support")
        super().__init__(agent_name, config)
        self._setup_support_capabilities()
        logger.info("Customer Support Agent initialized successfully")
    
    def _setup_support_capabilities(self) -> None:
        """Setup customer support specific capabilities."""
        capabilities = [
            "ticket_management",
            "knowledge_base_search",
            "issue_classification",
            "escalation_handling",
            "customer_interaction"
        ]
        
        for capability in capabilities:
            self.add_capability(capability)
        
        # Setup support-specific configuration
        self.max_escalation_level = self.config.get("max_escalation_level", 3)
        self.auto_escalate_keywords = self.config.get("auto_escalate_keywords", [
            "urgent", "critical", "emergency", "lawsuit", "legal"
        ])
        self.escalation_threshold = self.config.get("escalation_threshold", 0.3)

        # Setup agent persona and system prompt
        self.persona = self.config.get("persona", self._get_default_persona())
        self.system_prompt = self.config.get("system_prompt", self._get_default_system_prompt())

        # Setup LLM provider configuration
        self.llm_provider = self.config.get("llm_provider", "ollama")
        self.llm_model = self.config.get("llm_model", "gemma3:270m")

        logger.debug(f"Setup {len(capabilities)} support capabilities")

    def _get_default_persona(self) -> str:
        """Get the default persona for customer support agent."""
        return """You are Alex, a friendly and professional customer support specialist with 5+ years of experience.
        You are empathetic, patient, and solution-oriented. You have deep knowledge of products and services,
        and you're skilled at de-escalating tense situations. You always maintain a positive attitude and
        strive to exceed customer expectations."""

    def _get_default_system_prompt(self) -> str:
        """Get the default system prompt for customer support agent."""
        return """You are a professional customer support agent. Your role is to:

1. **Listen actively** - Understand the customer's issue completely before responding
2. **Show empathy** - Acknowledge the customer's frustration and validate their concerns
3. **Provide solutions** - Offer clear, actionable steps to resolve their issue
4. **Be proactive** - Anticipate follow-up questions and provide comprehensive answers
5. **Escalate when needed** - Know when to involve supervisors or specialists

**Guidelines:**
- Always greet customers warmly and professionally
- Use clear, jargon-free language
- Provide step-by-step instructions when applicable
- Offer alternatives if the primary solution isn't suitable
- Follow up to ensure satisfaction
- Document interactions for future reference

**Tone:** Professional, friendly, helpful, and patient
**Goal:** Resolve customer issues efficiently while maintaining high satisfaction"""

    def process(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a customer support query.
        
        Args:
            query: Customer's support query
            context: Additional context (customer info, history, etc.)
            
        Returns:
            Dict containing response, ticket info, and escalation status
            
        Raises:
            AgentError: If processing fails
        """
        try:
            logger.info(f"Processing support query: {query[:100]}...")
            
            # Classify the issue
            issue_type = self._classify_issue(query, context)
            
            # Check for escalation triggers
            needs_escalation = self._check_escalation_needed(query, context)
            
            # Generate response
            response = self._generate_response(query, context, issue_type)
            
            # Create or update ticket
            ticket_info = self._handle_ticket(query, context, issue_type)
            
            result = {
                "response": response,
                "ticket_id": ticket_info.get("ticket_id"),
                "issue_type": issue_type,
                "escalation": needs_escalation,
                "confidence": self._calculate_confidence(query, context),
                "suggested_actions": self._get_suggested_actions(issue_type)
            }
            
            logger.info(f"Support query processed successfully. Ticket: {ticket_info.get('ticket_id')}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to process support query: {str(e)}")
            raise AgentError(f"Support query processing failed: {str(e)}") from e
    
    def _classify_issue(self, query: str, context: Dict[str, Any]) -> str:
        """
        Classify the type of support issue.
        
        Args:
            query: Customer query
            context: Additional context
            
        Returns:
            Issue classification string
        """
        # Simple keyword-based classification (would be ML-based in production)
        query_lower = query.lower()
        
        if any(word in query_lower for word in ["password", "login", "access"]):
            return "authentication"
        elif any(word in query_lower for word in ["billing", "payment", "charge"]):
            return "billing"
        elif any(word in query_lower for word in ["bug", "error", "broken"]):
            return "technical"
        elif any(word in query_lower for word in ["refund", "cancel", "return"]):
            return "refund"
        else:
            return "general"
    
    def _check_escalation_needed(self, query: str, context: Dict[str, Any]) -> bool:
        """
        Check if the query needs escalation.
        
        Args:
            query: Customer query
            context: Additional context
            
        Returns:
            True if escalation is needed
        """
        query_lower = query.lower()
        
        # Check for escalation keywords
        if any(keyword in query_lower for keyword in self.auto_escalate_keywords):
            logger.info("Escalation triggered by keywords")
            return True
        
        # Check customer tier
        customer_tier = context.get("customer_tier", "standard")
        if customer_tier in ["premium", "enterprise"]:
            return True
        
        # Check previous escalations
        escalation_count = context.get("escalation_count", 0)
        if escalation_count >= self.max_escalation_level:
            return True
        
        return False
    
    def _generate_response(self, query: str, context: Dict[str, Any], issue_type: str) -> str:
        """
        Generate a response to the customer query.
        
        Args:
            query: Customer query
            context: Additional context
            issue_type: Classified issue type
            
        Returns:
            Generated response string
        """
        # Template-based response generation (would use LLM in production)
        templates = {
            "authentication": "I understand you're having trouble with login. Let me help you reset your credentials.",
            "billing": "I see you have a billing inquiry. Let me review your account details.",
            "technical": "I understand you're experiencing a technical issue. Let me investigate this for you.",
            "refund": "I see you're requesting a refund. Let me review your purchase and our refund policy.",
            "general": "Thank you for contacting support. I'm here to help with your inquiry."
        }
        
        base_response = templates.get(issue_type, templates["general"])
        
        # Personalize if customer name is available
        customer_name = context.get("customer_name")
        if customer_name:
            base_response = f"Hello {customer_name}, {base_response.lower()}"
        
        return base_response
    
    def _handle_ticket(self, query: str, context: Dict[str, Any], issue_type: str) -> Dict[str, Any]:
        """
        Create or update support ticket.
        
        Args:
            query: Customer query
            context: Additional context
            issue_type: Classified issue type
            
        Returns:
            Ticket information dictionary
        """
        import uuid
        
        ticket_id = context.get("ticket_id") or f"TICKET-{uuid.uuid4().hex[:8].upper()}"
        
        ticket_info = {
            "ticket_id": ticket_id,
            "issue_type": issue_type,
            "status": "open",
            "priority": self._determine_priority(query, context),
            "created_at": context.get("timestamp"),
            "customer_id": context.get("customer_id")
        }
        
        logger.debug(f"Ticket handled: {ticket_id}")
        return ticket_info
    
    def _determine_priority(self, query: str, context: Dict[str, Any]) -> str:
        """Determine ticket priority based on query and context."""
        if any(word in query.lower() for word in self.auto_escalate_keywords):
            return "high"
        elif context.get("customer_tier") in ["premium", "enterprise"]:
            return "medium"
        else:
            return "low"
    
    def _calculate_confidence(self, query: str, context: Dict[str, Any]) -> float:
        """Calculate confidence score for the response."""
        # Simple confidence calculation (would be ML-based in production)
        base_confidence = 0.7
        
        if len(query) > 50:  # More detailed queries get higher confidence
            base_confidence += 0.1
        
        if context.get("customer_history"):  # Historical context helps
            base_confidence += 0.1
        
        return min(base_confidence, 1.0)
    
    def _get_suggested_actions(self, issue_type: str) -> List[str]:
        """Get suggested actions based on issue type."""
        actions = {
            "authentication": ["Reset password", "Check account status", "Verify email"],
            "billing": ["Review charges", "Check payment method", "Generate invoice"],
            "technical": ["Check system status", "Review logs", "Test functionality"],
            "refund": ["Review purchase", "Check refund policy", "Process refund"],
            "general": ["Gather more information", "Search knowledge base"]
        }
        
        return actions.get(issue_type, actions["general"])

"""
Quick test script to verify the memory system is working correctly.
"""

import sys
import traceback
from app.memory.adapters import In<PERSON><PERSON>ory<PERSON>dapter, JSONLAdapter
from app.memory.base import <PERSON>Manager, ShortTermMemory, EpisodicMemory
from app.core.types import Message

def test_memory_adapters():
    """Test memory adapters functionality."""
    print("Testing Memory Adapters...")
    
    try:
        # Test InMemoryAdapter
        adapter = InMemoryAdapter()
        message = Message(role="user", content="Test message")
        
        # Test add_message method
        adapter.add_message("agent1", "user1", "session1", message)
        print("✓ InMemoryAdapter.add_message() works")
        
        # Test get_messages method
        messages = adapter.get_messages("agent1", "user1", "session1")
        assert len(messages) == 1
        assert messages[0]["content"] == "Test message"
        print("✓ InMemoryAdapter.get_messages() works")
        
        # Test log_event method
        event = {"action": "test", "result": "success"}
        adapter.log_event(event)
        print("✓ InMemoryAdapter.log_event() works")
        
        # Test add_procedure method
        procedure = {"name": "test_proc", "steps": ["step1", "step2"]}
        adapter.add_procedure("agent1", "user1", procedure)
        print("✓ InMemoryAdapter.add_procedure() works")
        
        # Test add_fact method
        fact = {"concept": "test", "value": "fact"}
        adapter.add_fact("agent1", "user1", fact)
        print("✓ InMemoryAdapter.add_fact() works")
        
        print("✓ All InMemoryAdapter methods work correctly")
        
    except Exception as e:
        print(f"✗ InMemoryAdapter test failed: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_memory_types():
    """Test memory types functionality."""
    print("\nTesting Memory Types...")
    
    try:
        # Test ShortTermMemory
        adapter = InMemoryAdapter()
        memory = ShortTermMemory("agent1", "user1", "session1", adapter=adapter)
        message = Message(role="user", content="Short term test")
        
        memory.add_message("agent1", "user1", "session1", message)
        messages = memory.get_messages("agent1", "user1", "session1")
        assert len(messages) == 1
        print("✓ ShortTermMemory works with adapter")
        
        # Test EpisodicMemory
        episodic = EpisodicMemory("agent1", "user1", "session1", adapter=adapter)
        event = {"action": "test_episodic", "result": "success"}
        episodic.log_event(event)
        episodes = episodic.get_episodes()
        assert len(episodes) >= 1
        print("✓ EpisodicMemory works with adapter")
        
    except Exception as e:
        print(f"✗ Memory types test failed: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_memory_manager():
    """Test MemoryManager functionality."""
    print("\nTesting MemoryManager...")
    
    try:
        # Test without adapters (fallback)
        manager = MemoryManager()
        agent_memory = manager.get_agent_memory("agent1", "user1", "session1")
        assert agent_memory is not None
        print("✓ MemoryManager works without adapters")
        
        # Test with adapters
        adapters = {
            "short_term": InMemoryAdapter(),
            "episodic": InMemoryAdapter(),
            "procedural": InMemoryAdapter(),
            "semantic": InMemoryAdapter()
        }
        
        manager = MemoryManager(adapters=adapters)
        
        # Test short-term message operations
        message = Message(role="user", content="Manager test message")
        manager.add_short_term_message("agent1", "user1", "session1", message)
        
        messages = manager.get_short_term_messages("agent1", "user1", "session1")
        assert len(messages) == 1
        assert messages[0]["content"] == "Manager test message"
        print("✓ MemoryManager short-term operations work")
        
        # Test episodic operations
        event = {"action": "manager_test", "result": "success"}
        manager.log_episode("agent1", "user1", "session1", event)
        print("✓ MemoryManager episodic operations work")
        
        # Test procedural operations
        procedure = {"steps": ["step1", "step2"]}
        manager.add_procedure("agent1", "user1", "test_proc", procedure)
        print("✓ MemoryManager procedural operations work")
        
        # Test semantic operations
        fact = {"concept": "test", "value": "fact"}
        manager.add_fact("agent1", "user1", fact)
        print("✓ MemoryManager semantic operations work")
        
    except Exception as e:
        print(f"✗ MemoryManager test failed: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_agent_integration():
    """Test agent integration with memory system."""
    print("\nTesting Agent Integration...")
    
    try:
        from app.agents.base import BaseAgent
        
        # Test agent memory setup
        memory_config = {
            "short_term": {"adapter": "in_memory"},
            "episodic": {"adapter": "in_memory"},
            "procedural": {"adapter": "in_memory"},
            "semantic": {"adapter": "in_memory"}
        }
        
        # This would test the actual agent integration
        # For now, just verify the memory config structure
        assert "short_term" in memory_config
        assert "episodic" in memory_config
        assert "procedural" in memory_config
        assert "semantic" in memory_config
        print("✓ Agent memory configuration structure is correct")
        
    except Exception as e:
        print(f"✗ Agent integration test failed: {e}")
        traceback.print_exc()
        return False
    
    return True

def main():
    """Run all memory system tests."""
    print("=" * 50)
    print("MEMORY SYSTEM VERIFICATION")
    print("=" * 50)
    
    tests = [
        test_memory_adapters,
        test_memory_types,
        test_memory_manager,
        test_agent_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"RESULTS: {passed} passed, {failed} failed")
    print("=" * 50)
    
    if failed == 0:
        print("🎉 All memory system tests passed!")
        return True
    else:
        print("❌ Some memory system tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

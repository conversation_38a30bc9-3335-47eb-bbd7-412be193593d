"""
Unit tests for policies module.

This module contains comprehensive unit tests for policy loading and management
functionality.
"""

import pytest
import tempfile
import yaml
import json
import os
from pathlib import Path
from unittest.mock import patch, mock_open

from app.policies.loader import load_policy, PolicyLoadError


class TestPolicyLoader:
    """Test cases for policy loading functionality."""
    
    def test_load_policy_yaml_file(self):
        """Test loading policy from YAML file."""
        policy_data = {
            "max_steps": 10,
            "budget_limit": 1000,
            "allowed_tools": ["http.get", "math.eval"],
            "timeout": 30,
            "retry_attempts": 3
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.safe_dump(policy_data, f)
            policy_path = f.name
        
        try:
            loaded_policy = load_policy(policy_path)
            
            assert loaded_policy["max_steps"] == 10
            assert loaded_policy["budget_limit"] == 1000
            assert loaded_policy["allowed_tools"] == ["http.get", "math.eval"]
            assert loaded_policy["timeout"] == 30
            assert loaded_policy["retry_attempts"] == 3
        finally:
            Path(policy_path).unlink()
    
    def test_load_policy_yml_file(self):
        """Test loading policy from .yml file."""
        policy_data = {
            "max_steps": 5,
            "allowed_tools": ["tool1", "tool2"]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False) as f:
            yaml.safe_dump(policy_data, f)
            policy_path = f.name
        
        try:
            loaded_policy = load_policy(policy_path)
            
            assert loaded_policy["max_steps"] == 5
            assert loaded_policy["allowed_tools"] == ["tool1", "tool2"]
        finally:
            Path(policy_path).unlink()
    
    def test_load_policy_json_file(self):
        """Test loading policy from JSON file."""
        policy_data = {
            "max_steps": 15,
            "budget_limit": 2000,
            "allowed_tools": ["json_tool"],
            "custom_setting": True
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(policy_data, f)
            policy_path = f.name
        
        try:
            loaded_policy = load_policy(policy_path)
            
            assert loaded_policy["max_steps"] == 15
            assert loaded_policy["budget_limit"] == 2000
            assert loaded_policy["allowed_tools"] == ["json_tool"]
            assert loaded_policy["custom_setting"] is True
        finally:
            Path(policy_path).unlink()
    
    def test_load_policy_nonexistent_file(self):
        """Test loading policy from non-existent file."""
        with pytest.raises(PolicyLoadError) as exc_info:
            load_policy("/nonexistent/path/policy.yaml")
        
        assert "Policy file not found" in str(exc_info.value)
        assert "/nonexistent/path/policy.yaml" in str(exc_info.value)
    
    def test_load_policy_invalid_yaml(self):
        """Test loading policy from invalid YAML file."""
        invalid_yaml = """
        max_steps: 10
        allowed_tools:
          - tool1
          - tool2
        invalid_yaml: [unclosed bracket
        """
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(invalid_yaml)
            policy_path = f.name
        
        try:
            with pytest.raises(PolicyLoadError) as exc_info:
                load_policy(policy_path)
            
            assert "Failed to parse YAML policy file" in str(exc_info.value)
        finally:
            Path(policy_path).unlink()
    
    def test_load_policy_invalid_json(self):
        """Test loading policy from invalid JSON file."""
        invalid_json = '{"max_steps": 10, "invalid_json": }'
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write(invalid_json)
            policy_path = f.name
        
        try:
            with pytest.raises(PolicyLoadError) as exc_info:
                load_policy(policy_path)
            
            assert "Failed to parse JSON policy file" in str(exc_info.value)
        finally:
            Path(policy_path).unlink()
    
    def test_load_policy_unsupported_format(self):
        """Test loading policy from unsupported file format."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("max_steps: 10")
            policy_path = f.name
        
        try:
            with pytest.raises(PolicyLoadError) as exc_info:
                load_policy(policy_path)
            
            assert "Unsupported policy file format" in str(exc_info.value)
            assert ".txt" in str(exc_info.value)
        finally:
            Path(policy_path).unlink()
    
    def test_load_policy_empty_file(self):
        """Test loading policy from empty file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            # Write empty content
            f.write("")
            policy_path = f.name
        
        try:
            loaded_policy = load_policy(policy_path)
            
            # Empty YAML file should return None, which we handle as empty dict
            assert loaded_policy is None or loaded_policy == {}
        finally:
            Path(policy_path).unlink()
    
    def test_load_policy_complex_structure(self):
        """Test loading policy with complex nested structure."""
        policy_data = {
            "max_steps": 20,
            "budget_limit": 5000,
            "allowed_tools": ["http.get", "math.eval", "custom.tool"],
            "validation": {
                "content_filters": ["password", "secret"],
                "token_limits": {
                    "input": 1000,
                    "output": 2000
                }
            },
            "retry_policy": {
                "max_attempts": 3,
                "backoff_factor": 2.0,
                "timeout": 30
            },
            "logging": {
                "level": "INFO",
                "include_sensitive": False
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.safe_dump(policy_data, f)
            policy_path = f.name
        
        try:
            loaded_policy = load_policy(policy_path)
            
            assert loaded_policy["max_steps"] == 20
            assert loaded_policy["budget_limit"] == 5000
            assert len(loaded_policy["allowed_tools"]) == 3
            
            # Test nested structures
            assert "validation" in loaded_policy
            assert loaded_policy["validation"]["content_filters"] == ["password", "secret"]
            assert loaded_policy["validation"]["token_limits"]["input"] == 1000
            assert loaded_policy["validation"]["token_limits"]["output"] == 2000
            
            assert "retry_policy" in loaded_policy
            assert loaded_policy["retry_policy"]["max_attempts"] == 3
            assert loaded_policy["retry_policy"]["backoff_factor"] == 2.0
            
            assert "logging" in loaded_policy
            assert loaded_policy["logging"]["level"] == "INFO"
            assert loaded_policy["logging"]["include_sensitive"] is False
        finally:
            Path(policy_path).unlink()
    
    def test_load_policy_with_environment_variables(self):
        """Test loading policy with environment variable substitution."""
        # Note: This test assumes the policy loader supports env var substitution
        # If not implemented, this test documents the expected behavior
        policy_data = {
            "max_steps": "${MAX_STEPS:10}",  # Default to 10 if not set
            "api_key": "${API_KEY}",
            "debug": "${DEBUG:false}"
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.safe_dump(policy_data, f)
            policy_path = f.name
        
        try:
            # Set environment variable
            os.environ["MAX_STEPS"] = "25"
            os.environ["API_KEY"] = "test-key"
            # Don't set DEBUG to test default
            
            loaded_policy = load_policy(policy_path)
            
            # If env var substitution is implemented:
            # assert loaded_policy["max_steps"] == "25"
            # assert loaded_policy["api_key"] == "test-key"
            # assert loaded_policy["debug"] == "false"
            
            # If not implemented, values remain as strings:
            assert isinstance(loaded_policy["max_steps"], str)
            assert isinstance(loaded_policy["api_key"], str)
            assert isinstance(loaded_policy["debug"], str)
            
        finally:
            Path(policy_path).unlink()
            # Clean up environment variables
            if "MAX_STEPS" in os.environ:
                del os.environ["MAX_STEPS"]
            if "API_KEY" in os.environ:
                del os.environ["API_KEY"]
    
    def test_load_policy_file_permissions(self):
        """Test loading policy with restricted file permissions."""
        policy_data = {"max_steps": 5}
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.safe_dump(policy_data, f)
            policy_path = f.name
        
        try:
            # Make file readable
            os.chmod(policy_path, 0o644)
            
            loaded_policy = load_policy(policy_path)
            assert loaded_policy["max_steps"] == 5
            
            # Make file unreadable (if running as non-root)
            if os.getuid() != 0:  # Skip if running as root
                os.chmod(policy_path, 0o000)
                
                with pytest.raises(PolicyLoadError) as exc_info:
                    load_policy(policy_path)
                
                assert "Permission denied" in str(exc_info.value) or "Failed to read policy file" in str(exc_info.value)
        finally:
            # Restore permissions for cleanup
            os.chmod(policy_path, 0o644)
            Path(policy_path).unlink()
    
    def test_load_policy_large_file(self):
        """Test loading policy from large file."""
        # Create a policy with many entries
        policy_data = {
            "max_steps": 100,
            "allowed_tools": [f"tool_{i}" for i in range(1000)],  # 1000 tools
            "large_config": {f"setting_{i}": f"value_{i}" for i in range(500)}  # 500 settings
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.safe_dump(policy_data, f)
            policy_path = f.name
        
        try:
            loaded_policy = load_policy(policy_path)
            
            assert loaded_policy["max_steps"] == 100
            assert len(loaded_policy["allowed_tools"]) == 1000
            assert len(loaded_policy["large_config"]) == 500
            assert loaded_policy["allowed_tools"][0] == "tool_0"
            assert loaded_policy["allowed_tools"][-1] == "tool_999"
        finally:
            Path(policy_path).unlink()
    
    def test_load_policy_unicode_content(self):
        """Test loading policy with Unicode content."""
        policy_data = {
            "max_steps": 10,
            "description": "Policy with Unicode: 你好世界 🌍 café naïve résumé",
            "allowed_tools": ["tool_ñ", "tool_中文", "tool_🔧"],
            "messages": {
                "welcome": "Bienvenido! 欢迎! مرحبا!",
                "error": "Erreur! 错误! خطأ!"
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False, encoding='utf-8') as f:
            yaml.safe_dump(policy_data, f, allow_unicode=True)
            policy_path = f.name
        
        try:
            loaded_policy = load_policy(policy_path)
            
            assert loaded_policy["max_steps"] == 10
            assert "你好世界" in loaded_policy["description"]
            assert "🌍" in loaded_policy["description"]
            assert "tool_ñ" in loaded_policy["allowed_tools"]
            assert "tool_中文" in loaded_policy["allowed_tools"]
            assert "tool_🔧" in loaded_policy["allowed_tools"]
            assert "Bienvenido!" in loaded_policy["messages"]["welcome"]
            assert "欢迎!" in loaded_policy["messages"]["welcome"]
        finally:
            Path(policy_path).unlink()


class TestPolicyValidation:
    """Test cases for policy validation (if implemented)."""
    
    def test_policy_schema_validation(self):
        """Test policy schema validation."""
        # This test documents expected behavior for policy validation
        # If validation is not implemented, this serves as a specification
        
        valid_policy = {
            "max_steps": 10,
            "budget_limit": 1000,
            "allowed_tools": ["http.get", "math.eval"],
            "timeout": 30
        }
        
        invalid_policies = [
            {"max_steps": -1},  # Negative steps
            {"budget_limit": "invalid"},  # Wrong type
            {"allowed_tools": "not_a_list"},  # Wrong type
            {"timeout": -5},  # Negative timeout
        ]
        
        # If validation is implemented, these should pass/fail accordingly
        # For now, we just document the expected structure
        assert isinstance(valid_policy["max_steps"], int)
        assert valid_policy["max_steps"] > 0
        assert isinstance(valid_policy["allowed_tools"], list)
        assert all(isinstance(tool, str) for tool in valid_policy["allowed_tools"])
    
    def test_policy_required_fields(self):
        """Test policy required fields validation."""
        # Document which fields should be required
        minimal_policy = {
            "max_steps": 5
        }
        
        # This should be valid (minimal required fields)
        assert "max_steps" in minimal_policy
        assert isinstance(minimal_policy["max_steps"], int)
        assert minimal_policy["max_steps"] > 0

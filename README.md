# AgentKit - Agentic AI Framework

A comprehensive, production-ready Agentic AI framework with **Configuration-Driven Agent Creation**, **Multi-Agent Orchestration**, and **Enterprise-Grade Features**.

## 🚀 Phase 1 Features

### ✅ Core Framework
- **Agent Factory Pattern**: Create specialized agents (Customer Support, Sales, Research) with configuration-driven setup
- **Multi-Agent Orchestration**: Coordinate multiple agents for complex workflows
- **Production-Ready API**: RESTful endpoints with comprehensive error handling and validation
- **Memory Systems**: In-memory buffer, persistent JSONL storage, and Qdrant vector database integration
- **Tool Registry**: Extensible tool system with HTTP and math evaluation tools
- **Validation Framework**: Step limits, token budgets, and content filtering
- **Policy Management**: YAML/JSON configuration for agent behavior and constraints
- **LLM Provider Support**: OpenAI, Ollama, and extensible provider architecture

### ✅ Specialized Agents
- **Customer Support Agent**: Ticket management, sentiment analysis, escalation handling
- **Sales Agent**: Lead qualification, product recommendations, pricing negotiations
- **Research Agent**: Multi-source information gathering, fact verification, report generation

### ✅ API Endpoints
- **Agent Management**: Create, list, update, delete agents
- **Configuration Management**: Dynamic agent configuration updates
- **Health Monitoring**: Agent health checks and status monitoring
- **Bulk Operations**: Bulk agent creation and management
- **Multi-Agent Collaboration**: Coordinate multiple agents for complex tasks

### ✅ Enterprise Features
- **Comprehensive Testing**: 100+ unit tests and integration tests
- **Production Logging**: Structured logging with context tracking
- **Error Handling**: Centralized exception management
- **Configuration Validation**: Schema validation for agent configurations
- **Documentation**: Complete API documentation and examples

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Layer    │    │  Agent Factory  │    │ Specialized     │
│                │    │                 │    │ Agents          │
│ • REST API     │────│ • Agent Types   │────│ • Customer      │
│ • Validation   │    │ • Configuration │    │   Support       │
│ • Error        │    │ • Registration  │    │ • Sales         │
│   Handling     │    │                 │    │ • Research      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │  Core Runner    │              │
         └──────────────│                 │──────────────┘
                        │ • Planning      │
                        │ • Execution     │
                        │ • State Mgmt    │
                        └─────────────────┘
                                 │
    ┌─────────────┬──────────────┼──────────────┬─────────────┐
    │             │              │              │             │
┌───▼───┐  ┌─────▼─────┐  ┌─────▼─────┐  ┌─────▼─────┐  ┌───▼───┐
│ Tools │  │  Memory   │  │Validation │  │ Policies  │  │ LLM   │
│       │  │           │  │           │  │           │  │Providers│
│• HTTP │  │• Buffer   │  │• Limits   │  │• YAML     │  │• OpenAI│
│• Math │  │• JSONL    │  │• Content  │  │• JSON     │  │• Ollama│
└───────┘  └───────────┘  └───────────┘  └───────────┘  └───────┘
```

## 📦 Installation

### Prerequisites
- Python 3.10+
- Virtual environment (recommended)

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd agentkit

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -e ".[openai]"

# Set up environment
cp .env.example .env
# Edit .env with your API keys

# Run the application
make run
# or
uvicorn app.api.main:app --host 0.0.0.0 --port 8000 --reload

# Open API documentation
# http://localhost:8000/docs
```

### Optional Dependencies
```bash
# For different LLM providers
pip install -e ".[openai]"     # OpenAI GPT models
pip install -e ".[ollama]"     # Ollama local models
pip install -e ".[mistral]"    # Mistral AI models
pip install -e ".[gemini]"     # Google Gemini models
pip install -e ".[zhipu]"      # ZhipuAI models

# For development
pip install -e ".[dev]"        # Development dependencies

# Additional tools
pip install requests           # Required for SearxNG web search tool
```

## ⚙️ Configuration

### Environment Variables
Create a `.env` file in the project root:

```bash
# LLM Provider Configuration
LLM_PROVIDER=openai                    # openai, ollama, mistral, gemini, zhipu
OPENAI_API_KEY=your_openai_api_key     # Required for OpenAI
OLLAMA_BASE_URL=http://localhost:11434 # Required for Ollama

# Optional Configuration
LOG_LEVEL=INFO                         # DEBUG, INFO, WARNING, ERROR
MAX_WORKERS=4                          # Number of worker threads
```

### Agent Configuration Files
Create YAML or JSON configuration files for custom agent behavior:

```yaml
# config/customer_support.yaml
tools:
  - "http.get"
  - "knowledge_base.search"
memory:
  type: "buffer"
  max_size: 1000
planner:
  type: "react"
  max_steps: 10
escalation_threshold: 0.3
max_resolution_attempts: 3
supported_languages:
  - "en"
  - "es"
  - "fr"
```

## 🎭 Enhanced Agent Features

### Agent Personas and System Prompts

Each agent now comes with a detailed persona and system prompt for more natural interactions:

```yaml
# Enhanced agent configuration
persona: |
  You are Alex, a friendly and professional customer support specialist with 5+ years of experience.
  You are empathetic, patient, and solution-oriented.

system_prompt: |
  You are a professional customer support agent. Your role is to:
  1. Listen actively and understand customer issues
  2. Show empathy and validate concerns
  3. Provide clear solutions and actionable steps
  4. Be proactive and anticipate follow-up questions
  5. Escalate when needed

# LLM Provider Configuration
llm_provider: "ollama"
llm_model: "gemma3:270m"
```

### Session-Based Memory Management

Agents now maintain conversation context across interactions:

```python
# Chat with session continuity
response = requests.post("http://localhost:8000/agents/{agent_id}/chat", json={
    "message": "Hello, I need help with my order",
    "session_id": "user_123_session",  # Optional - auto-generated if not provided
    "context": {"user_id": "123", "order_id": "ORD-456"}
})
```

## 🧠 Comprehensive Memory System

AgentKit features a sophisticated multi-layered memory architecture designed for production-ready agent applications with four distinct memory types and multiple storage adapters.

### Memory Architecture Overview

The memory system provides a unified interface for different types of memory storage:

```
┌─────────────────────────────────────────────────────────────┐
│                    Memory Manager                           │
├─────────────────┬─────────────────┬─────────────────┬───────┤
│  Short-term     │   Episodic      │   Procedural    │Semantic│
│   Memory        │    Memory       │    Memory       │Memory │
│                 │                 │                 │       │
│• Conversation   │• Event Logs     │• Workflows      │• Facts│
│• Session Context│• Interactions   │• Procedures     │• Search│
│• Temporary Data │• User Behavior  │• Step-by-step   │• Knowledge│
└─────────────────┴─────────────────┴─────────────────┴───────┘
         │                 │                 │           │
┌────────▼─────────────────▼─────────────────▼───────────▼────┐
│                 Storage Adapters                            │
│ InMemory │ JSONL │ SQLite │ Redis │ VectorDB │ S3 │ MongoDB │
└─────────────────────────────────────────────────────────────┘
```

### Memory Types

#### 🔄 Short-term Memory
- **Purpose**: Recent conversation history and session context
- **Scope**: Agent + User + Session specific
- **Retention**: Session-based (cleared when session ends)
- **Use Cases**: Chat history, current conversation context, temporary variables

#### 📚 Episodic Memory
- **Purpose**: Event logging and experience tracking
- **Scope**: Agent + User + Session specific
- **Retention**: Persistent across sessions
- **Use Cases**: Interaction logs, user behavior patterns, task completion history

#### ⚙️ Procedural Memory
- **Purpose**: Process and workflow storage
- **Scope**: Agent + User specific (shared across sessions)
- **Retention**: Long-term persistent
- **Use Cases**: Learned procedures, custom workflows, step-by-step guides

#### 🔍 Semantic Memory
- **Purpose**: Knowledge and fact storage with semantic search
- **Scope**: Agent + User specific (shared across sessions)
- **Retention**: Long-term persistent
- **Use Cases**: Facts, concepts, knowledge base, semantic search

### Memory Adapters

#### Available Storage Backends

| Adapter | Use Case | Persistence | Performance | Scalability |
|---------|----------|-------------|-------------|-------------|
| **InMemory** | Development, Testing | ❌ | ⚡ Very Fast | 🔸 Single Process |
| **JSONL** | Simple Persistence | ✅ | 🟡 Fast | 🔸 Single Node |
| **SQLite** | Local Database | ✅ | 🟡 Fast | 🔸 Single Node |
| **Redis** | Distributed Cache | ✅ | ⚡ Very Fast | 🟢 Clustered |
| **VectorDB** | Semantic Search | ✅ | 🟡 Fast | 🟢 Clustered |
| **PostgreSQL** | Enterprise DB | ✅ | 🟡 Fast | 🟢 Clustered |
| **MongoDB** | Document Store | ✅ | 🟡 Fast | 🟢 Clustered |
| **S3** | Cloud Storage | ✅ | 🔴 Slow | 🟢 Unlimited |

### Configuration Examples

#### Development Configuration
```python
memory_config = {
    "short_term": {"adapter": "in_memory"},
    "episodic": {"adapter": "in_memory"},
    "procedural": {"adapter": "in_memory"},
    "semantic": {"adapter": "in_memory"}
}
```

#### Production Configuration
```python
memory_config = {
    "short_term": {
        "adapter": "redis",
        "host": "redis-cluster.internal",
        "port": 6379,
        "ttl": 3600  # 1 hour session timeout
    },
    "episodic": {
        "adapter": "postgresql",
        "connection_string": "******************************/episodic"
    },
    "procedural": {
        "adapter": "sqlite",
        "db_path": "./data/procedures.db"
    },
    "semantic": {
        "adapter": "vectordb",
        "url": "http://qdrant-cluster:6333",
        "collection": "agent_knowledge"
    }
}
```

### Vector Database Memory (Qdrant Integration)

Enhanced semantic memory with vector database support:

```bash
# Set up Qdrant for vector memory
docker run -d -p 6333:6333 qdrant/qdrant

# Configure agent with comprehensive memory
curl -X POST "http://localhost:8000/agent-factory/agents/create" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_type": "customer_support",
    "name": "comprehensive_memory_agent",
    "config": {
      "memory": {
        "short_term": {"adapter": "in_memory"},
        "episodic": {"adapter": "jsonl", "jsonl_path": "./logs/episodes.jsonl"},
        "procedural": {"adapter": "sqlite", "db_path": "./data/procedures.db"},
        "semantic": {
          "adapter": "vectordb",
          "url": "http://localhost:6333",
          "collection": "agent_knowledge",
          "vector_size": 768,
          "distance": "Cosine"
        }
      }
    }
  }'
```

**Enhanced Memory Features:**
- **Multi-Type Integration**: All four memory types working together
- **Semantic Search**: Vector-based similarity search for knowledge retrieval
- **Persistent Storage**: Long-term memory that survives agent restarts
- **HTTP Fallback**: Automatic fallback to HTTP API when client methods fail
- **Session Isolation**: Separate short-term memory per session
- **Cross-Session Persistence**: Episodic, procedural, and semantic memory shared across sessions
- **Error Resilience**: Comprehensive error handling with graceful degradation
- **Production Ready**: Scalable architecture with proper logging and monitoring

### Web Search Integration (SearxNG)

Research agents can now perform comprehensive web searches:

```bash
# Set up SearxNG for web search
docker run -d -p 8080:8080 searxng/searxng

# Configure in .env
SEARXNG_URL=http://localhost:8080
```

### Available Tools

- **http_get**: HTTP requests and API calls
- **searxng_search**: Privacy-focused web search
- **math_eval**: Mathematical calculations and data analysis

### LLM Provider Integration

Agents use Ollama with the gemma3:270m model by default:

```bash
# Install and setup Ollama
curl -fsSL https://ollama.ai/install.sh | sh
ollama pull gemma3:270m
ollama serve
```

## 🔧 API Usage

### Agent Management

#### Create an Agent
```bash
curl -X POST "http://localhost:8000/agents/create" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_type": "customer_support",
    "name": "support_agent_1",
    "config": {
      "escalation_threshold": 0.4,
      "max_resolution_attempts": 5
    }
  }'
```

#### List All Agents
```bash
curl -X GET "http://localhost:8000/agents/"
```

#### Get Agent Information
```bash
curl -X GET "http://localhost:8000/agents/{agent_id}"
```

#### Chat with an Agent
```bash
curl -X POST "http://localhost:8000/agents/{agent_id}/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "I need help with a refund",
    "context": {
      "customer_id": "cust_123",
      "order_id": "order_456"
    }
  }'
```

#### Update Agent Configuration
```bash
curl -X PUT "http://localhost:8000/agents/{agent_id}/config" \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "escalation_threshold": 0.5
    },
    "merge": true
  }'
```

#### Health Check
```bash
curl -X GET "http://localhost:8000/agents/{agent_id}/health"
```

#### Delete an Agent
```bash
curl -X DELETE "http://localhost:8000/agents/{agent_id}"
```

### Bulk Operations

#### Bulk Create Agents
```bash
curl -X POST "http://localhost:8000/agents/bulk-create" \
  -H "Content-Type: application/json" \
  -d '{
    "agents": [
      {
        "agent_type": "customer_support",
        "name": "support_1"
      },
      {
        "agent_type": "sales",
        "name": "sales_1",
        "config": {
          "qualification_threshold": 0.8
        }
      }
    ]
  }'
```

### Multi-Agent Collaboration

#### Coordinate Multiple Agents
```bash
curl -X POST "http://localhost:8000/agents/multi-agent/collaborate" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Help a customer who wants to buy our premium product",
    "agents": ["support_agent_id", "sales_agent_id"],
    "collaboration_mode": "sequential"
  }'
```

## 🤖 Agent Types

### Customer Support Agent
Specialized for customer service operations with advanced sentiment analysis and escalation handling.

**Capabilities:**
- Ticket creation and management
- Sentiment analysis and emotional intelligence
- Knowledge base search and retrieval
- Automatic escalation based on complexity and sentiment
- Multi-language support
- Resolution tracking and follow-up

**Default Configuration:**
```yaml
escalation_threshold: 0.3
max_resolution_attempts: 3
supported_languages: ["en"]
ticket_priority_mapping:
  high: ["urgent", "critical", "emergency"]
  medium: ["important", "refund", "billing"]
  low: ["general", "information", "question"]
```

**Example Usage:**
```python
from app.factory.agent_factory import AgentFactory

# Create customer support agent
agent = AgentFactory.create_agent(
    agent_type="customer_support",
    config={
        "escalation_threshold": 0.4,
        "supported_languages": ["en", "es"],
        "max_resolution_attempts": 5
    }
)

# Process customer inquiry
result = agent.process(
    query="I'm very frustrated with my recent order",
    context={
        "customer_id": "cust_123",
        "order_id": "order_456",
        "previous_interactions": 2
    }
)
```

### Sales Agent
Optimized for sales operations with lead qualification and deal management capabilities.

**Capabilities:**
- Lead qualification and scoring
- Product recommendation engine
- Pricing negotiation and discount management
- Deal pipeline tracking
- Customer profiling and segmentation
- Sales stage progression

**Default Configuration:**
```yaml
qualification_threshold: 0.7
max_discount: 0.15
product_catalog: []
sales_stages: ["lead", "qualified", "proposal", "negotiation", "closed"]
```

**Example Usage:**
```python
# Create sales agent
agent = AgentFactory.create_agent(
    agent_type="sales",
    config={
        "qualification_threshold": 0.8,
        "max_discount": 0.2,
        "product_catalog": ["premium", "standard", "basic"]
    }
)

# Process sales inquiry
result = agent.process(
    query="What are your pricing options for enterprise customers?",
    context={
        "customer_id": "enterprise_123",
        "budget": 50000,
        "decision_maker": True,
        "company_size": "large"
    }
)
```

### Research Agent
Designed for comprehensive research and information gathering with fact verification.

**Capabilities:**
- Multi-source information gathering
- Data analysis and synthesis
- Fact verification and confidence scoring
- Citation management and source tracking
- Report generation with structured findings
- Trend analysis and pattern recognition

**Default Configuration:**
```yaml
max_sources: 10
fact_check_threshold: 0.8
research_depth: "standard"
citation_style: "apa"
confidence_threshold: 0.7
```

**Example Usage:**
```python
# Create research agent
agent = AgentFactory.create_agent(
    agent_type="research",
    config={
        "max_sources": 15,
        "research_depth": "comprehensive",
        "citation_style": "mla"
    }
)

# Process research query
result = agent.process(
    query="What are the latest trends in artificial intelligence?",
    context={
        "research_type": "trend_analysis",
        "time_frame": "last_6_months",
        "depth": "comprehensive"
    }
)
```

## 🛠️ Development

### Running Tests
```bash
# Run all tests
pytest

# Run specific test categories
pytest tests/unit/                    # Unit tests
pytest tests/integration/             # Integration tests

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test file
pytest tests/unit/test_agents.py
```

### Code Quality
```bash
# Format code
make fmt

# Lint code
make lint

# Type checking
mypy app/
```

### Development Setup
```bash
# Install development dependencies
pip install -e ".[dev]"

# Pre-commit hooks
pre-commit install

# Run development server with auto-reload
uvicorn app.api.main:app --reload --host 0.0.0.0 --port 8000
```

## 📚 Examples

### Basic Agent Creation and Usage
```python
from app.factory.agent_factory import AgentFactory

# Create a customer support agent
support_agent = AgentFactory.create_agent("customer_support")

# Process a customer inquiry
result = support_agent.process(
    query="I can't access my account",
    context={"customer_id": "cust_123"}
)

print(f"Response: {result['response']}")
print(f"Ticket ID: {result['ticket']['ticket_id']}")
print(f"Escalation needed: {result['escalation_required']}")
```

### Multi-Agent Workflow
```python
from app.factory.agent_factory import AgentFactory
from app.orchestrators.multi_agent import MultiAgentOrchestrator

# Create multiple agents
support_agent = AgentFactory.create_agent("customer_support")
sales_agent = AgentFactory.create_agent("sales")
research_agent = AgentFactory.create_agent("research")

# Create orchestrator
orchestrator = MultiAgentOrchestrator([support_agent, sales_agent, research_agent])

# Process complex query requiring multiple agents
result = await orchestrator.process_collaborative(
    "A customer wants to upgrade their plan but has billing issues"
)
```

### Custom Agent Configuration
```python
# Create agent with custom configuration
custom_config = {
    "escalation_threshold": 0.2,  # Lower threshold for faster escalation
    "max_resolution_attempts": 7,
    "supported_languages": ["en", "es", "fr"],
    "custom_tools": ["knowledge_base", "crm_integration"],
    "notification_settings": {
        "email_alerts": True,
        "slack_integration": True
    }
}

agent = AgentFactory.create_agent(
    agent_type="customer_support",
    config=custom_config
)
```

### Using Configuration Files
```python
# Load agent from configuration file
agent = AgentFactory.create_agent(
    agent_type="sales",
    config_path="config/sales_enterprise.yaml"
)

# Save configuration template
AgentFactory.save_config_template(
    agent_type="research",
    output_path="config/research_template.yaml"
)
```

## 🔍 Monitoring and Observability

### Health Checks
All agents provide health check endpoints for monitoring:

```python
# Check agent health
health = agent.health_check()
print(f"Status: {health['status']}")
print(f"Uptime: {health['uptime']} seconds")
print(f"Capabilities: {health['capabilities_count']}")
```

### Logging
Structured logging with context tracking:

```python
import logging
from app.observability.logging import setup_logging

# Configure logging
setup_logging(level="INFO")

# Logs include context information
logger = logging.getLogger(__name__)
logger.info("Agent processing started", extra={
    "agent_id": "agent_123",
    "query_type": "customer_inquiry",
    "context": {"customer_id": "cust_456"}
})
```

### Metrics and Performance
Monitor agent performance and usage:

```python
# Agent execution metrics are automatically tracked
result = agent.process(query, context)

# Access performance metrics
print(f"Processing time: {result['metadata']['processing_time']}")
print(f"Token usage: {result['metadata']['token_usage']}")
print(f"Tools used: {result['metadata']['tools_used']}")
```

## 🚀 Deployment

### Docker Deployment
```bash
# Build Docker image
docker build -t agentkit .

# Run container
docker run -p 8000:8000 --env-file .env agentkit

# Using Docker Compose
docker-compose up -d
```

### Production Configuration
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  agentkit:
    build: .
    ports:
      - "8000:8000"
    environment:
      - LLM_PROVIDER=openai
      - LOG_LEVEL=INFO
      - MAX_WORKERS=8
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    restart: unless-stopped
```

### Environment-Specific Settings
```bash
# Production
export LLM_PROVIDER=openai
export LOG_LEVEL=WARNING
export MAX_WORKERS=16

# Development
export LLM_PROVIDER=ollama
export LOG_LEVEL=DEBUG
export MAX_WORKERS=4
```

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Add tests for new functionality
5. Run the test suite: `pytest`
6. Commit your changes: `git commit -m 'Add amazing feature'`
7. Push to the branch: `git push origin feature/amazing-feature`
8. Open a Pull Request

### Code Standards
- Follow PEP 8 style guidelines
- Add type hints to all functions
- Write comprehensive docstrings
- Maintain test coverage above 90%
- Update documentation for new features

### Testing Requirements
- Unit tests for all new modules
- Integration tests for API endpoints
- Performance tests for critical paths
- Error handling tests for edge cases

## 🔧 Recent Fixes & Improvements

### Qdrant Vector Database Integration (Latest)
- **Fixed Qdrant Initialization Error**: Resolved `NotImplementedError` when initializing Qdrant collections
- **HTTP Fallback Implementation**: Added comprehensive HTTP fallback methods for all Qdrant operations when client methods are not implemented
- **Production-Ready Error Handling**: Implemented robust error handling with detailed logging for all vector database operations
- **UUID Point ID Support**: Added automatic conversion of string keys to deterministic UUIDs for Qdrant point IDs
- **Comprehensive Testing**: Updated unit tests and integration tests for VectorDB adapter functionality
- **Memory System Enhancement**: Enhanced semantic memory with reliable vector database persistence

### Key Technical Improvements:
- **VectorDBAdapter**: Complete rewrite with HTTP fallback pattern for maximum compatibility
- **Collection Management**: Automatic collection creation and management with proper vector configuration
- **Vector Operations**: Support for add, search, clear, and collection info operations via both client and HTTP methods
- **API Integration**: Seamless integration with agent creation API for VectorDB-backed semantic memory
- **Test Coverage**: 100% test coverage for VectorDB functionality with comprehensive mocking

### Verified Functionality:
- ✅ Agent creation with VectorDB memory configuration
- ✅ Vector storage and retrieval operations
- ✅ HTTP fallback when Qdrant client methods are not implemented
- ✅ Collection lifecycle management (create, clear, delete)
- ✅ Error handling and logging for all failure scenarios
- ✅ Integration with existing memory system architecture

## 📄 License


## 🙏 Acknowledgments

- Built with FastAPI for high-performance API development
- Powered by Pydantic for data validation and settings management
- Supports multiple LLM providers for flexibility
- Inspired by modern agentic AI architectures and best practices

## 📞 Support

- **Documentation**: [API Docs](http://localhost:8000/docs)
- **Issues**: [GitHub Issues](https://github.com/your-repo/agentkit/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-repo/agentkit/discussions)

---

**AgentKit** - Building the future of agentic AI, one agent at a time. 🤖✨

from __future__ import annotations
from typing import List, Dict, Any
from ..core.types import Plan, Action, Message

class MockPlanner:
    """Very simple planner: if 'http' in goal → http.get; if '+' in goal → math.eval."""
    def plan(self, goal: str, history: List[Message], state: Dict[str, Any]) -> Plan:
        actions = []
        if "http" in goal.lower() or "fetch" in goal.lower() or "url" in goal.lower():
            # naive pick
            url = "https://httpbin.org/get"
            actions.append(Action(tool="http.get", params={"url": url}, reason="Fetch sample URL"))
        if any(op in goal for op in ["+", "-", "*", "/", "**"]):
            expr = "2+3" if "2+3" in goal else "2+3"
            actions.append(Action(tool="math.eval", params={"expr": expr}, reason="Compute arithmetic"))
        return Plan(actions=actions, rationale="Mock plan")

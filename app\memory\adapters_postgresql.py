"""
PostgreSQLAdapter for agent memory storage.
"""
from typing import Any, Dict, Optional
import psycopg2
import psycopg2.extras

class PostgreSQLAdapter:
    def __init__(self, dsn: str, table: str):
        self.conn = psycopg2.connect(dsn)
        self.table = table
        self._ensure_table()

    def _ensure_table(self):
        with self.conn.cursor() as cur:
            cur.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.table} (
                    key TEXT PRIMARY KEY,
                    value JSONB
                )
            """)
            self.conn.commit()

    def save(self, key: str, value: Dict[str, Any]):
        with self.conn.cursor() as cur:
            cur.execute(f"""
                INSERT INTO {self.table} (key, value) VALUES (%s, %s)
                ON CONFLICT (key) DO UPDATE SET value = EXCLUDED.value
            """, (key, psycopg2.extras.Json(value)))
            self.conn.commit()

    def load(self, key: str) -> Optional[Dict[str, Any]]:
        with self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            cur.execute(f"SELECT value FROM {self.table} WHERE key = %s", (key,))
            row = cur.fetchone()
            return row["value"] if row else None

    def delete(self, key: str):
        with self.conn.cursor() as cur:
            cur.execute(f"DELETE FROM {self.table} WHERE key = %s", (key,))
            self.conn.commit()

    def list_keys(self):
        with self.conn.cursor() as cur:
            cur.execute(f"SELECT key FROM {self.table}")
            return [row[0] for row in cur.fetchall()]

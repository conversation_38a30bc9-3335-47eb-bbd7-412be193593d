"""
Unit tests for tools module.

This module contains comprehensive unit tests for all tool implementations
including ToolRegistry, http_get, math_eval, and base tool functionality.
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

from app.tools.registry import ToolRegistry
from app.tools.base import Tool, ToolSpec
from app.tools.http_get import http_get
from app.tools.math_eval import math_eval
from app.core.exceptions import ToolError


class TestToolRegistry:
    """Test cases for ToolRegistry class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.registry = ToolRegistry()
    
    def test_initialization(self):
        """Test ToolRegistry initialization."""
        assert isinstance(self.registry._tools, dict)
        assert len(self.registry._tools) == 0
    
    def test_register_tool(self):
        """Test registering a tool."""
        mock_tool = Mock()
        mock_tool.spec = ToolSpec(name="test_tool", description="Test tool")
        
        self.registry.register(mock_tool)
        
        assert "test_tool" in self.registry._tools
        assert self.registry._tools["test_tool"] == mock_tool
    
    def test_register_duplicate_tool(self):
        """Test registering a tool with duplicate name."""
        mock_tool1 = Mock()
        mock_tool1.spec = ToolSpec(name="duplicate_tool", description="First tool")
        
        mock_tool2 = Mock()
        mock_tool2.spec = ToolSpec(name="duplicate_tool", description="Second tool")
        
        self.registry.register(mock_tool1)
        
        # Should overwrite the first tool
        self.registry.register(mock_tool2)
        
        assert self.registry._tools["duplicate_tool"] == mock_tool2
    
    def test_get_existing_tool(self):
        """Test getting an existing tool."""
        mock_tool = Mock()
        mock_tool.spec = ToolSpec(name="existing_tool", description="Existing tool")
        
        self.registry.register(mock_tool)
        retrieved_tool = self.registry.get("existing_tool")
        
        assert retrieved_tool == mock_tool
    
    def test_get_nonexistent_tool(self):
        """Test getting a non-existent tool."""
        retrieved_tool = self.registry.get("nonexistent_tool")
        assert retrieved_tool is None
    
    def test_list_tools_empty(self):
        """Test listing tools when registry is empty."""
        tools = self.registry.list()
        assert isinstance(tools, list)
        assert len(tools) == 0
    
    def test_list_tools_with_registered_tools(self):
        """Test listing tools with registered tools."""
        mock_tool1 = Mock()
        mock_tool1.spec = ToolSpec(name="tool1", description="First tool")
        
        mock_tool2 = Mock()
        mock_tool2.spec = ToolSpec(name="tool2", description="Second tool")
        
        self.registry.register(mock_tool1)
        self.registry.register(mock_tool2)
        
        tools = self.registry.list()
        
        assert len(tools) == 2
        assert mock_tool1 in tools
        assert mock_tool2 in tools
    
    def test_has_tool_existing(self):
        """Test checking if tool exists."""
        mock_tool = Mock()
        mock_tool.spec = ToolSpec(name="check_tool", description="Tool to check")
        
        self.registry.register(mock_tool)
        
        assert self.registry.has("check_tool") is True
        assert self.registry.has("nonexistent_tool") is False
    
    def test_remove_tool(self):
        """Test removing a tool."""
        mock_tool = Mock()
        mock_tool.spec = ToolSpec(name="removable_tool", description="Tool to remove")
        
        self.registry.register(mock_tool)
        assert self.registry.has("removable_tool") is True
        
        removed = self.registry.remove("removable_tool")
        
        assert removed == mock_tool
        assert self.registry.has("removable_tool") is False
    
    def test_remove_nonexistent_tool(self):
        """Test removing a non-existent tool."""
        removed = self.registry.remove("nonexistent_tool")
        assert removed is None
    
    def test_clear_registry(self):
        """Test clearing all tools from registry."""
        mock_tool1 = Mock()
        mock_tool1.spec = ToolSpec(name="tool1", description="First tool")
        
        mock_tool2 = Mock()
        mock_tool2.spec = ToolSpec(name="tool2", description="Second tool")
        
        self.registry.register(mock_tool1)
        self.registry.register(mock_tool2)
        
        assert len(self.registry.list()) == 2
        
        self.registry.clear()
        
        assert len(self.registry.list()) == 0
        assert len(self.registry._tools) == 0


class TestMathEvalTool:
    """Test cases for math_eval tool."""
    
    def test_math_eval_spec(self):
        """Test math_eval tool specification."""
        assert math_eval.spec.name == "math.eval"
        assert "mathematical expression" in math_eval.spec.description.lower()
        assert "expression" in math_eval.spec.parameters["properties"]
    
    def test_simple_arithmetic(self):
        """Test simple arithmetic operations."""
        test_cases = [
            ("2 + 3", 5),
            ("10 - 4", 6),
            ("6 * 7", 42),
            ("15 / 3", 5),
            ("2 ** 3", 8),
            ("17 % 5", 2)
        ]
        
        for expression, expected in test_cases:
            result = math_eval.execute({"expression": expression})
            assert result["result"] == expected
            assert result["expression"] == expression
    
    def test_complex_expressions(self):
        """Test complex mathematical expressions."""
        test_cases = [
            ("(2 + 3) * 4", 20),
            ("2 + 3 * 4", 14),
            ("(10 - 2) / 2", 4),
            ("2 ** (3 + 1)", 16),
            ("abs(-5)", 5),
            ("max(1, 2, 3)", 3),
            ("min(1, 2, 3)", 1)
        ]
        
        for expression, expected in test_cases:
            result = math_eval.execute({"expression": expression})
            assert result["result"] == expected
    
    def test_mathematical_functions(self):
        """Test mathematical functions."""
        import math
        
        test_cases = [
            ("sqrt(16)", 4),
            ("ceil(3.2)", 4),
            ("floor(3.8)", 3),
            ("round(3.7)", 4),
            ("abs(-10)", 10)
        ]
        
        for expression, expected in test_cases:
            result = math_eval.execute({"expression": expression})
            assert result["result"] == expected
    
    def test_invalid_expression(self):
        """Test handling of invalid expressions."""
        invalid_expressions = [
            "2 +",  # Incomplete expression
            "2 / 0",  # Division by zero
            "invalid_function()",  # Unknown function
            "2 + 'string'",  # Type error
            "",  # Empty expression
        ]
        
        for expression in invalid_expressions:
            with pytest.raises(ToolError):
                math_eval.execute({"expression": expression})
    
    def test_security_restrictions(self):
        """Test that dangerous operations are restricted."""
        dangerous_expressions = [
            "__import__('os').system('ls')",  # Import attempt
            "exec('print(1)')",  # Exec attempt
            "eval('2+2')",  # Nested eval
            "open('/etc/passwd')",  # File access
        ]
        
        for expression in dangerous_expressions:
            with pytest.raises(ToolError):
                math_eval.execute({"expression": expression})
    
    def test_missing_expression_parameter(self):
        """Test handling of missing expression parameter."""
        with pytest.raises(ToolError):
            math_eval.execute({})
    
    def test_non_string_expression(self):
        """Test handling of non-string expression."""
        with pytest.raises(ToolError):
            math_eval.execute({"expression": 123})


class TestHttpGetTool:
    """Test cases for http_get tool."""
    
    def test_http_get_spec(self):
        """Test http_get tool specification."""
        assert http_get.spec.name == "http.get"
        assert "http get request" in http_get.spec.description.lower()
        assert "url" in http_get.spec.parameters["properties"]
    
    @patch('httpx.get')
    def test_successful_http_request(self, mock_get):
        """Test successful HTTP GET request."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "Success response"
        mock_response.headers = {"Content-Type": "text/plain"}
        mock_get.return_value = mock_response
        
        result = http_get.execute({"url": "https://example.com"})
        
        assert result["status_code"] == 200
        assert result["content"] == "Success response"
        assert result["url"] == "https://example.com"
        assert "headers" in result
        
        mock_get.assert_called_once_with("https://example.com", timeout=30)
    
    @patch('httpx.get')
    def test_http_request_with_custom_timeout(self, mock_get):
        """Test HTTP GET request with custom timeout."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "Success"
        mock_response.headers = {}
        mock_get.return_value = mock_response
        
        result = http_get.execute({"url": "https://example.com", "timeout": 10})
        
        assert result["status_code"] == 200
        mock_get.assert_called_once_with("https://example.com", timeout=10)
    
    @patch('httpx.get')
    def test_http_request_error(self, mock_get):
        """Test HTTP GET request with network error."""
        mock_get.side_effect = Exception("Network error")
        
        with pytest.raises(ToolError) as exc_info:
            http_get.execute({"url": "https://example.com"})
        
        assert "Network error" in str(exc_info.value)
    
    @patch('httpx.get')
    def test_http_request_404(self, mock_get):
        """Test HTTP GET request with 404 response."""
        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.text = "Not Found"
        mock_response.headers = {}
        mock_get.return_value = mock_response
        
        result = http_get.execute({"url": "https://example.com/notfound"})
        
        assert result["status_code"] == 404
        assert result["content"] == "Not Found"
    
    def test_missing_url_parameter(self):
        """Test handling of missing URL parameter."""
        with pytest.raises(ToolError):
            http_get.execute({})
    
    def test_invalid_url(self):
        """Test handling of invalid URL."""
        with pytest.raises(ToolError):
            http_get.execute({"url": "not-a-valid-url"})
    
    @patch('httpx.get')
    def test_json_response(self, mock_get):
        """Test handling of JSON response."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '{"key": "value", "number": 42}'
        mock_response.headers = {"Content-Type": "application/json"}
        mock_get.return_value = mock_response
        
        result = http_get.execute({"url": "https://api.example.com/data"})
        
        assert result["status_code"] == 200
        assert "application/json" in result["headers"]["Content-Type"]
        # Content should be the raw text, not parsed JSON
        assert result["content"] == '{"key": "value", "number": 42}'


class TestToolBase:
    """Test cases for base Tool functionality."""
    
    def test_tool_spec_creation(self):
        """Test ToolSpec creation."""
        spec = ToolSpec(
            name="test_tool",
            description="A test tool",
            parameters={
                "type": "object",
                "properties": {
                    "param1": {"type": "string"},
                    "param2": {"type": "integer"}
                },
                "required": ["param1"]
            }
        )
        
        assert spec.name == "test_tool"
        assert spec.description == "A test tool"
        assert "param1" in spec.parameters["properties"]
        assert "param2" in spec.parameters["properties"]
        assert spec.parameters["required"] == ["param1"]
    
    def test_tool_spec_validation(self):
        """Test ToolSpec parameter validation."""
        spec = ToolSpec(
            name="validation_tool",
            description="Tool for testing validation",
            parameters={
                "type": "object",
                "properties": {
                    "required_param": {"type": "string"}
                },
                "required": ["required_param"]
            }
        )
        
        # Valid parameters should pass
        valid_params = {"required_param": "test_value"}
        # This would be validated by the tool implementation
        
        # Invalid parameters should fail
        invalid_params = {}  # Missing required parameter
        # This would be caught by the tool implementation

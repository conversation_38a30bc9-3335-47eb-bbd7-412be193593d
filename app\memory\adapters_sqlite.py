"""
SQLite Storage Adapter for AgentKit Memory

Provides persistent, queryable storage for episodic and procedural memory.
"""
import sqlite3
import json
import logging
import os
from typing import Any, List, Optional
from .adapters import BaseMemoryAdapter

logger = logging.getLogger(__name__)


class SQLiteAdapter(BaseMemoryAdapter):
    """
    SQLite storage adapter for persistent, queryable memory.

    Provides local persistent storage with SQL querying capabilities,
    suitable for single-instance deployments requiring data persistence.
    """

    def __init__(self, db_path: str):
        """
        Initialize the SQLite adapter.

        Args:
            db_path: Path to the SQLite database file
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(db_path), exist_ok=True)

            self.db_path = db_path
            self.conn = sqlite3.connect(db_path, check_same_thread=False)
            self.conn.row_factory = sqlite3.Row  # Enable column access by name
            self._init_tables()
            logger.info(f"SQLiteAdapter initialized with database: {db_path}")
        except Exception as e:
            logger.error(f"Failed to initialize SQLite connection: {e}")
            raise

    def _init_tables(self) -> None:
        """Initialize the database tables."""
        try:
            cur = self.conn.cursor()
            cur.execute("""
                CREATE TABLE IF NOT EXISTS memory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT NOT NULL,
                    data TEXT NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Create index for better query performance
            cur.execute("""
                CREATE INDEX IF NOT EXISTS idx_memory_key_timestamp
                ON memory(key, timestamp DESC)
            """)

            self.conn.commit()
            logger.debug("SQLite tables initialized")
        except Exception as e:
            logger.error(f"Failed to initialize SQLite tables: {e}")
            raise

    def add(self, key: str, item: Any) -> None:
        """
        Add an item to SQLite database.

        Args:
            key: Storage key
            item: Item to store
        """
        try:
            cur = self.conn.cursor()
            serialized_item = json.dumps(item)
            cur.execute(
                "INSERT INTO memory (key, data) VALUES (?, ?)",
                (key, serialized_item)
            )
            self.conn.commit()
            logger.debug(f"Added item to SQLite storage: {key}")
        except Exception as e:
            logger.error(f"Failed to add item to SQLite storage: {e}")
            raise

    def get(self, key: str, limit: Optional[int] = None) -> List[Any]:
        """
        Retrieve items from SQLite database.

        Args:
            key: Storage key
            limit: Maximum number of items to retrieve

        Returns:
            List of stored items
        """
        try:
            cur = self.conn.cursor()
            sql = "SELECT data FROM memory WHERE key=? ORDER BY timestamp DESC"

            if limit:
                sql += " LIMIT ?"
                cur.execute(sql, (key, limit))
            else:
                cur.execute(sql, (key,))

            result = []
            for row in cur.fetchall():
                try:
                    deserialized_item = json.loads(row[0])
                    result.append(deserialized_item)
                except json.JSONDecodeError as e:
                    logger.warning(f"Skipping invalid JSON item in SQLite: {e}")
                    continue

            # Reverse to maintain chronological order (oldest first)
            result.reverse()

            logger.debug(f"Retrieved {len(result)} items from SQLite storage: {key}")
            return result

        except Exception as e:
            logger.error(f"Failed to retrieve items from SQLite storage: {e}")
            return []

    def clear(self, key: str) -> None:
        """
        Clear all items for a key from SQLite database.

        Args:
            key: Storage key to clear
        """
        try:
            cur = self.conn.cursor()
            cur.execute("DELETE FROM memory WHERE key=?", (key,))
            self.conn.commit()
            logger.debug(f"Cleared SQLite storage: {key}")
        except Exception as e:
            logger.error(f"Failed to clear SQLite storage: {e}")
            raise

    def __del__(self):
        """Clean up database connection."""
        try:
            if hasattr(self, 'conn'):
                self.conn.close()
        except Exception:
            pass  # Ignore cleanup errors

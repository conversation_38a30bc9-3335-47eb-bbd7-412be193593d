#!/usr/bin/env python3
"""
Debug script to understand Qdrant client initialization issues.
"""

import traceback
from qdrant_client import QdrantClient

def debug_qdrant_client():
    """Debug Qdrant client initialization."""
    print("Debugging Qdrant client initialization...")
    
    try:
        print("1. Testing basic QdrantClient initialization...")
        
        # Test 1: Basic initialization with host/port
        print("   - Trying host/port initialization...")
        client1 = QdrantClient(host="localhost", port=6333)
        print(f"   - Client type: {type(client1)}")
        print(f"   - Client class: {client1.__class__}")
        print(f"   - Client module: {client1.__class__.__module__}")
        
        # Test 2: URL-based initialization
        print("   - Trying URL-based initialization...")
        client2 = QdrantClient(url="http://localhost:6333")
        print(f"   - Client type: {type(client2)}")
        print(f"   - Client class: {client2.__class__}")
        print(f"   - Client module: {client2.__class__.__module__}")
        
        # Test 3: Explicit prefer_grpc=False
        print("   - Trying with prefer_grpc=False...")
        client3 = QdrantClient(host="localhost", port=6333, prefer_grpc=False)
        print(f"   - Client type: {type(client3)}")
        print(f"   - Client class: {client3.__class__}")
        print(f"   - Client module: {client3.__class__.__module__}")
        
        # Test methods on each client
        for i, client in enumerate([client1, client2, client3], 1):
            print(f"\n2. Testing client {i} methods...")
            try:
                print("   - Testing get_collections()...")
                collections = client.get_collections()
                print(f"   - Success! Collections: {collections}")
            except Exception as e:
                print(f"   - get_collections() failed: {type(e).__name__}: {e}")
            
            try:
                print("   - Testing create_collection()...")
                from qdrant_client.models import VectorParams, Distance
                client.create_collection(
                    collection_name="debug_test",
                    vectors_config=VectorParams(size=768, distance=Distance.COSINE)
                )
                print("   - create_collection() succeeded!")
                
                # Clean up
                client.delete_collection("debug_test")
                print("   - Cleanup successful!")
                
            except Exception as e:
                print(f"   - create_collection() failed: {type(e).__name__}: {e}")
                
    except Exception as e:
        print(f"Debug failed: {e}")
        traceback.print_exc()

def test_direct_http():
    """Test direct HTTP requests to Qdrant."""
    print("\n3. Testing direct HTTP requests...")
    
    try:
        import requests
        
        # Test collections endpoint
        response = requests.get("http://localhost:6333/collections")
        print(f"   - GET /collections: {response.status_code}")
        print(f"   - Response: {response.json()}")
        
        # Test create collection
        collection_data = {
            "vectors": {
                "size": 768,
                "distance": "Cosine"
            }
        }
        response = requests.put(
            "http://localhost:6333/collections/debug_http_test",
            json=collection_data
        )
        print(f"   - PUT /collections/debug_http_test: {response.status_code}")
        if response.status_code != 200:
            print(f"   - Error response: {response.text}")
        
        # Clean up
        response = requests.delete("http://localhost:6333/collections/debug_http_test")
        print(f"   - DELETE /collections/debug_http_test: {response.status_code}")
        
    except Exception as e:
        print(f"Direct HTTP test failed: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    print("=" * 60)
    print("QDRANT CLIENT DEBUG")
    print("=" * 60)
    
    debug_qdrant_client()
    test_direct_http()
